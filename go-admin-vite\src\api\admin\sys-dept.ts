import { request } from '@/utils/request'
import type { BaseQuery } from '@/types'

export interface SysDept {
  deptId: number
  parentId: number
  ancestors: string
  deptName: string
  orderNum: number
  leader: string
  phone: string
  email: string
  status: string
  createTime: string
  updateTime: string
  children?: SysDept[]
}

export interface DeptQuery extends BaseQuery {
  deptName?: string
  status?: string
}

/**
 * 查询部门列表
 */
export function listDept(query?: DeptQuery) {
  return request.get<SysDept[]>('/api/v1/dept', { params: query })
}

/**
 * 查询部门详细
 */
export function getDept(deptId: number) {
  return request.get<SysDept>(`/api/v1/dept/${deptId}`)
}

/**
 * 查询部门下拉树结构
 */
export function treeselect() {
  return request.get<SysDept[]>('/api/v1/deptTree')
}

/**
 * 根据角色ID查询部门树结构
 */
export function roleDeptTreeselect(roleId: number) {
  return request.get<{
    checkedKeys: number[]
    depts: SysDept[]
  }>(`/api/v1/roleDeptTreeselect/${roleId}`)
}

/**
 * 新增部门
 */
export function addDept(data: Partial<SysDept>) {
  return request.post('/api/v1/dept', data)
}

/**
 * 修改部门
 */
export function updateDept(data: Partial<SysDept>) {
  return request.put(`/api/v1/dept/${data.deptId}`, data)
}

/**
 * 删除部门
 */
export function delDept(deptId: number) {
  return request.delete(`/api/v1/dept/${deptId}`)
}
