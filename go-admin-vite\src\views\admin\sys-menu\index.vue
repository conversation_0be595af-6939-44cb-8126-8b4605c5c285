<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>菜单管理</span>
        </div>
      </template>
      
      <!-- 查询条件 -->
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
        <el-form-item label="菜单名称" prop="menuName">
          <el-input
            v-model="queryParams.menuName"
            placeholder="请输入菜单名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="菜单状态" clearable style="width: 200px">
            <el-option label="正常" value="0" />
            <el-option label="停用" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" plain icon="Sort" @click="toggleExpandAll">展开/折叠</el-button>
        </el-col>
      </el-row>

      <!-- 菜单表格 -->
      <el-table
        v-loading="loading"
        :data="menuList"
        row-key="menuId"
        :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column label="菜单名称" align="left" prop="menuName" :show-overflow-tooltip="true" />
        <el-table-column label="图标" align="center" prop="icon" width="100">
          <template #default="scope">
            <el-icon v-if="scope.row.icon">
              <component :is="scope.row.icon" />
            </el-icon>
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center" prop="sort" />
        <el-table-column label="权限标识" align="center" prop="permission" :show-overflow-tooltip="true" />
        <el-table-column label="组件路径" align="center" prop="component" :show-overflow-tooltip="true" />
        <el-table-column label="状态" align="center" key="visible">
          <template #default="scope">
            <el-tag :type="scope.row.visible === '0' ? 'success' : 'danger'">
              {{ scope.row.visible === '0' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              link
            >
              修改
            </el-button>
            <el-button
              type="primary"
              icon="Plus"
              @click="handleAdd(scope.row)"
              link
            >
              新增
            </el-button>
            <el-button
              type="primary"
              icon="Delete"
              @click="handleDelete(scope.row)"
              link
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { parseTime } from '@/utils'

// 模拟数据
const loading = ref(false)
const isExpandAll = ref(false)
const menuList = ref([
  {
    menuId: 1,
    menuName: '系统管理',
    icon: 'System',
    sort: 1,
    permission: '',
    component: 'Layout',
    visible: '0',
    createTime: '2023-01-01 00:00:00',
    children: [
      {
        menuId: 100,
        menuName: '用户管理',
        icon: 'User',
        sort: 1,
        permission: 'admin:sysUser:list',
        component: 'admin/sys-user/index',
        visible: '0',
        createTime: '2023-01-01 00:00:00',
      },
      {
        menuId: 101,
        menuName: '角色管理',
        icon: 'UserFilled',
        sort: 2,
        permission: 'admin:sysRole:list',
        component: 'admin/sys-role/index',
        visible: '0',
        createTime: '2023-01-01 00:00:00',
      },
    ],
  },
  {
    menuId: 2,
    menuName: '开发工具',
    icon: 'Monitor',
    sort: 2,
    permission: '',
    component: 'Layout',
    visible: '0',
    createTime: '2023-01-01 00:00:00',
    children: [
      {
        menuId: 200,
        menuName: 'Swagger文档',
        icon: 'Document',
        sort: 1,
        permission: 'dev:swagger:list',
        component: 'dev-tools/swagger/index',
        visible: '0',
        createTime: '2023-01-01 00:00:00',
      },
    ],
  },
])

const queryParams = reactive({
  menuName: '',
  status: '',
})

const queryFormRef = ref<FormInstance>()

function getList() {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 500)
}

function handleQuery() {
  getList()
}

function resetQuery() {
  queryFormRef.value?.resetFields()
  handleQuery()
}

function toggleExpandAll() {
  isExpandAll.value = !isExpandAll.value
}

function handleAdd(row?: any) {
  if (row) {
    ElMessage.success(`为菜单 "${row.title}" 添加子菜单`)
  } else {
    ElMessage.success('添加顶级菜单')
  }
  console.log('新增菜单:', row)
}

function handleUpdate(row: any) {
  ElMessage.success(`编辑菜单: ${row.title}`)
  console.log('编辑菜单:', row)
}

function handleDelete(row: any) {
  ElMessageBox.confirm(
    `确定要删除菜单 "${row.title}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success('删除成功')
    getList() // 重新加载数据
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
</style>
