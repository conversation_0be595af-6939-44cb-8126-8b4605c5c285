<template>
  <span>{{ displayValue }}</span>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'

interface Props {
  startVal?: number
  endVal: number
  duration?: number
  autoplay?: boolean
  decimals?: number
  decimal?: string
  separator?: string
  prefix?: string
  suffix?: string
  useEasing?: boolean
  easingFn?: (t: number, b: number, c: number, d: number) => number
}

const props = withDefaults(defineProps<Props>(), {
  startVal: 0,
  duration: 3000,
  autoplay: true,
  decimals: 0,
  decimal: '.',
  separator: ',',
  prefix: '',
  suffix: '',
  useEasing: true,
  easingFn: (t: number, b: number, c: number, d: number) => {
    return (c * (-Math.pow(2, (-10 * t) / d) + 1) * 1024) / 1023 + b
  },
})

const localStartVal = ref(props.startVal)
const displayValue = ref(formatNumber(props.startVal))
const printVal = ref<number | null>(null)
const paused = ref(false)
const localDuration = ref(props.duration)
const startTime = ref<number | null>(null)
const timestamp = ref<number | null>(null)
const remaining = ref<number | null>(null)
const rAF = ref<number | null>(null)

const countDown = computed(() => props.startVal > props.endVal)

watch(() => props.startVal, () => {
  if (props.autoplay) {
    start()
  }
})

watch(() => props.endVal, () => {
  if (props.autoplay) {
    start()
  }
})

function start() {
  localStartVal.value = props.startVal
  startTime.value = null
  localDuration.value = props.duration
  paused.value = false
  rAF.value = requestAnimationFrame(count)
}

function pauseResume() {
  if (paused.value) {
    resume()
    paused.value = false
  } else {
    pause()
    paused.value = true
  }
}

function pause() {
  if (rAF.value) {
    cancelAnimationFrame(rAF.value)
  }
}

function resume() {
  startTime.value = null
  localDuration.value = +(remaining.value || 0)
  localStartVal.value = +(printVal.value || 0)
  requestAnimationFrame(count)
}

function reset() {
  startTime.value = null
  if (rAF.value) {
    cancelAnimationFrame(rAF.value)
  }
  displayValue.value = formatNumber(props.startVal)
}

function count(timestamp_: number) {
  if (!startTime.value) startTime.value = timestamp_
  timestamp.value = timestamp_
  const progress = timestamp_ - startTime.value
  remaining.value = localDuration.value - progress

  if (props.useEasing) {
    if (countDown.value) {
      printVal.value =
        localStartVal.value -
        props.easingFn(progress, 0, localStartVal.value - props.endVal, localDuration.value)
    } else {
      printVal.value = props.easingFn(
        progress,
        localStartVal.value,
        props.endVal - localStartVal.value,
        localDuration.value
      )
    }
  } else {
    if (countDown.value) {
      printVal.value =
        localStartVal.value - (localStartVal.value - props.endVal) * (progress / localDuration.value)
    } else {
      printVal.value =
        localStartVal.value + (props.endVal - localStartVal.value) * (progress / localDuration.value)
    }
  }

  if (countDown.value) {
    printVal.value = printVal.value < props.endVal ? props.endVal : printVal.value
  } else {
    printVal.value = printVal.value > props.endVal ? props.endVal : printVal.value
  }

  displayValue.value = formatNumber(printVal.value || 0)
  if (progress < localDuration.value) {
    rAF.value = requestAnimationFrame(count)
  }
}

function formatNumber(num: number): string {
  const numStr = num.toFixed(props.decimals)
  const parts = numStr.split('.')
  parts[0] = parts[0].replace(/(\d)(?=(\d{3})+(?!\d))/g, `$1${props.separator}`)
  return props.prefix + parts.join(props.decimal) + props.suffix
}

onMounted(() => {
  if (props.autoplay) {
    start()
  }
  displayValue.value = formatNumber(props.startVal)
})

defineExpose({
  start,
  pause,
  resume,
  reset,
  pauseResume,
})
</script>
