<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>代码生成器</span>
        </div>
      </template>
      
      <!-- 查询条件 -->
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
        <el-form-item label="表名称" prop="tableName">
          <el-input
            v-model="queryParams.tableName"
            placeholder="请输入表名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="表描述" prop="tableComment">
          <el-input
            v-model="queryParams.tableComment"
            placeholder="请输入表描述"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Download"
            :disabled="multiple"
            @click="handleGenTable"
          >生成</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="Upload"
            @click="handleImportTable"
          >导入</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleEditTable"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
          >删除</el-button>
        </el-col>
      </el-row>

      <!-- 表格数据 -->
      <el-table
        v-loading="loading"
        :data="tableList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column
          label="表名称"
          align="center"
          prop="tableName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="表描述"
          align="center"
          prop="tableComment"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="实体"
          align="center"
          prop="className"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="创建时间" align="center" prop="createTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" align="center" prop="updateTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button
              type="primary"
              icon="View"
              @click="handlePreview(scope.row)"
              link
            >预览</el-button>
            <el-button
              type="primary"
              icon="Edit"
              @click="handleEditTable(scope.row)"
              link
            >编辑</el-button>
            <el-button
              type="primary"
              icon="Delete"
              @click="handleDelete(scope.row)"
              link
            >删除</el-button>
            <el-button
              type="primary"
              icon="Download"
              @click="handleGenTable(scope.row)"
              link
            >生成代码</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination/index.vue'

// 模拟数据
const loading = ref(false)
const tableList = ref([
  {
    tableId: 1,
    tableName: 'sys_user',
    tableComment: '用户信息表',
    className: 'SysUser',
    createTime: '2023-01-01 00:00:00',
    updateTime: '2023-01-01 00:00:00',
  },
  {
    tableId: 2,
    tableName: 'sys_role',
    tableComment: '角色信息表',
    className: 'SysRole',
    createTime: '2023-01-01 00:00:00',
    updateTime: '2023-01-01 00:00:00',
  },
])

const total = ref(2)
const single = ref(true)
const multiple = ref(true)

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  tableName: '',
  tableComment: '',
})

const queryFormRef = ref<FormInstance>()

function getList() {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 500)
}

function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

function resetQuery() {
  queryFormRef.value?.resetFields()
  handleQuery()
}

function handleSelectionChange(selection: any[]) {
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

function handleGenTable(row?: any) {
  ElMessage.info('代码生成功能待开发')
}

function handleImportTable() {
  ElMessage.info('导入表功能待开发')
}

function handleEditTable(row?: any) {
  ElMessage.info('编辑表功能待开发')
}

function handleDelete(row?: any) {
  ElMessage.info('删除功能待开发')
}

function handlePreview(row: any) {
  ElMessage.info('预览功能待开发')
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
</style>
