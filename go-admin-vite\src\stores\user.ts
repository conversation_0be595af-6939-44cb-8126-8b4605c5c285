import { ref } from 'vue'
import { defineStore } from 'pinia'
import { request } from '@/utils/request'
import type { User, LoginForm } from '@/types'

export const useUserStore = defineStore('user', () => {
  const token = ref<string>(localStorage.getItem('token') || '')
  const userInfo = ref<User | null>(null)
  const roles = ref<string[]>([])
  const permissions = ref<string[]>([])

  // 登录
  async function login(loginForm: LoginForm): Promise<void> {
    try {
      const response = await request.post<any>('/api/v1/login', loginForm)
      console.log('登录响应:', response)

      // 后端返回格式: { code: 200, token: "xxx", expire: "xxx" }
      if (response && response.token) {
        token.value = response.token
        localStorage.setItem('token', response.token)
      } else {
        throw new Error('登录响应格式错误: ' + JSON.stringify(response))
      }
    } catch (error) {
      throw error
    }
  }

  // 获取用户信息
  async function getUserInfo(): Promise<void> {
    try {
      const data = await request.get<any>('/api/v1/getinfo')
      console.log('用户信息响应:', data)

      // 后端返回的是扁平的用户信息对象
      if (data) {
        // 构造用户信息对象
        userInfo.value = {
          userId: data.userId,
          username: data.userName,
          nickName: data.name,
          email: data.email || '',
          phone: data.phone || '',
          sex: data.sex || '0',
          avatar: data.avatar || '',
          status: '2', // 默认正常状态
          deptId: data.deptId,
          postIds: [],
          roleIds: data.roles ? [data.roleId] : [],
          remark: '',
          createTime: '',
          updateTime: '',
        }

        // 设置角色和权限
        roles.value = data.roles || []
        permissions.value = data.permissions || []
      } else {
        throw new Error('获取用户信息失败')
      }
    } catch (error) {
      throw error
    }
  }

  // 登出
  async function logout(): Promise<void> {
    try {
      await request.post('/api/v1/logout')
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      token.value = ''
      userInfo.value = null
      roles.value = []
      permissions.value = []
      localStorage.removeItem('token')
    }
  }

  // 重置状态
  function resetState(): void {
    token.value = ''
    userInfo.value = null
    roles.value = []
    permissions.value = []
    localStorage.removeItem('token')
  }

  return {
    token,
    userInfo,
    roles,
    permissions,
    login,
    getUserInfo,
    logout,
    resetState,
  }
})
