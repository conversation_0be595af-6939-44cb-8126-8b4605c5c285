import { request } from '@/utils/request'
import type { Base<PERSON>uery, PageResult } from '@/types'

export interface SysUser {
  userId: number
  username: string
  nickName: string
  email: string
  phone: string
  sex: string
  avatar: string
  password?: string
  status: string
  deptId: number
  postIds: number[]
  roleIds: number[]
  remark: string
  createTime: string
  updateTime: string
  dept?: {
    deptId: number
    deptName: string
  }
}

export interface UserQuery extends BaseQuery {
  username?: string
  phone?: string
  status?: string
  deptId?: number
  beginTime?: string
  endTime?: string
}

/**
 * 查询用户列表
 */
export function listUser(query: UserQuery) {
  return request.get<PageResult<SysUser>>('/api/v1/sys-user', { params: query })
}

/**
 * 查询用户详细
 */
export function getUser(userId: number) {
  return request.get<SysUser>(`/api/v1/sys-user/${userId}`)
}

/**
 * 获取用户初始化数据
 */
export function getUserInit() {
  return request.get<{
    posts: any[]
    roles: any[]
    user: SysUser
  }>('/api/v1/sys-user/')
}

/**
 * 新增用户
 */
export function addUser(data: Partial<SysUser>) {
  return request.post('/api/v1/sys-user', data)
}

/**
 * 修改用户
 */
export function updateUser(data: Partial<SysUser>) {
  return request.put('/api/v1/sys-user', data)
}

/**
 * 删除用户
 */
export function delUser(userIds: number[]) {
  return request.delete('/api/v1/sys-user', { data: userIds })
}

/**
 * 导出用户
 */
export function exportUser(query: UserQuery) {
  return request.get('/api/v1/sys-user/export', { params: query })
}

/**
 * 用户密码重置
 */
export function resetUserPwd(userId: number, password: string) {
  const data = {
    userId,
    password
  }
  return request.put('/api/v1/user/pwd/reset', data)
}

/**
 * 用户状态修改
 */
export function changeUserStatus(userId: number, status: string) {
  const data = {
    userId,
    status
  }
  return request.put('/api/v1/user/status', data)
}

/**
 * 修改用户个人信息
 */
export function updateUserProfile(data: Partial<SysUser>) {
  return request.put('/api/v1/sys-user/profile', data)
}

/**
 * 下载用户导入模板
 */
export function importTemplate() {
  return request.get('/api/v1/sys-user/importTemplate')
}
