<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>部门管理</span>
        </div>
      </template>
      
      <!-- 查询条件 -->
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
        <el-form-item label="部门名称" prop="deptName">
          <el-input
            v-model="queryParams.deptName"
            placeholder="请输入部门名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="部门状态" clearable style="width: 200px">
            <el-option label="正常" value="0" />
            <el-option label="停用" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" plain icon="Sort" @click="toggleExpandAll">展开/折叠</el-button>
        </el-col>
      </el-row>

      <!-- 部门表格 -->
      <el-table
        v-loading="loading"
        :data="deptList"
        row-key="deptId"
        :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column label="部门名称" align="left" prop="deptName" />
        <el-table-column label="排序" align="center" prop="orderNum" />
        <el-table-column label="负责人" align="center" prop="leader" />
        <el-table-column label="联系电话" align="center" prop="phone" />
        <el-table-column label="邮箱" align="center" prop="email" />
        <el-table-column label="状态" align="center" key="status">
          <template #default="scope">
            <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
              {{ scope.row.status === '0' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              link
            >
              修改
            </el-button>
            <el-button
              type="primary"
              icon="Plus"
              @click="handleAdd(scope.row)"
              link
            >
              新增
            </el-button>
            <el-button
              type="primary"
              icon="Delete"
              @click="handleDelete(scope.row)"
              link
              v-if="scope.row.parentId !== 0"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { parseTime } from '@/utils'

// 模拟数据
const loading = ref(false)
const isExpandAll = ref(false)
const deptList = ref([
  {
    deptId: 100,
    deptName: '总公司',
    parentId: 0,
    orderNum: 0,
    leader: '张三',
    phone: '15888888888',
    email: '<EMAIL>',
    status: '0',
    createTime: '2023-01-01 00:00:00',
    children: [
      {
        deptId: 101,
        deptName: '技术部',
        parentId: 100,
        orderNum: 1,
        leader: '李四',
        phone: '15888888889',
        email: '<EMAIL>',
        status: '0',
        createTime: '2023-01-01 00:00:00',
        children: [
          {
            deptId: 103,
            deptName: '前端组',
            parentId: 101,
            orderNum: 1,
            leader: '王五',
            phone: '15888888890',
            email: '<EMAIL>',
            status: '0',
            createTime: '2023-01-01 00:00:00',
          },
          {
            deptId: 104,
            deptName: '后端组',
            parentId: 101,
            orderNum: 2,
            leader: '赵六',
            phone: '15888888891',
            email: '<EMAIL>',
            status: '0',
            createTime: '2023-01-01 00:00:00',
          },
        ],
      },
      {
        deptId: 102,
        deptName: '市场部',
        parentId: 100,
        orderNum: 2,
        leader: '钱七',
        phone: '15888888892',
        email: '<EMAIL>',
        status: '0',
        createTime: '2023-01-01 00:00:00',
      },
    ],
  },
])

const queryParams = reactive({
  deptName: '',
  status: '',
})

const queryFormRef = ref<FormInstance>()

function getList() {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 500)
}

function handleQuery() {
  getList()
}

function resetQuery() {
  queryFormRef.value?.resetFields()
  handleQuery()
}

function toggleExpandAll() {
  isExpandAll.value = !isExpandAll.value
}

function handleAdd(row?: any) {
  ElMessage.info('新增功能待开发')
}

function handleUpdate(row: any) {
  ElMessage.info('修改功能待开发')
}

function handleDelete(row: any) {
  ElMessage.info('删除功能待开发')
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
</style>
