/**
 * 判断是否为外部链接
 * @param path 路径
 */
export function isExternal(path: string): boolean {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * 验证用户名
 * @param str 用户名
 */
export function validUsername(str: string): boolean {
  const validMap = ['admin', 'editor']
  return validMap.indexOf(str.trim()) >= 0
}

/**
 * 验证URL
 * @param url URL地址
 */
export function validURL(url: string): boolean {
  const reg = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return reg.test(url)
}

/**
 * 验证小写字母
 * @param str 字符串
 */
export function validLowerCase(str: string): boolean {
  const reg = /^[a-z]+$/
  return reg.test(str)
}

/**
 * 验证大写字母
 * @param str 字符串
 */
export function validUpperCase(str: string): boolean {
  const reg = /^[A-Z]+$/
  return reg.test(str)
}

/**
 * 验证字母
 * @param str 字符串
 */
export function validAlphabets(str: string): boolean {
  const reg = /^[A-Za-z]+$/
  return reg.test(str)
}

/**
 * 验证邮箱
 * @param email 邮箱
 */
export function validEmail(email: string): boolean {
  const reg = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return reg.test(email)
}

/**
 * 验证手机号
 * @param phone 手机号
 */
export function validPhone(phone: string): boolean {
  const reg = /^1[3|4|5|6|7|8|9][0-9]\d{8}$/
  return reg.test(phone)
}

/**
 * 验证身份证号
 * @param idCard 身份证号
 */
export function validIdCard(idCard: string): boolean {
  const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return reg.test(idCard)
}

/**
 * 验证密码强度
 * @param password 密码
 */
export function validPassword(password: string): boolean {
  // 至少8位，包含大小写字母、数字和特殊字符
  const reg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
  return reg.test(password)
}

/**
 * 验证IP地址
 * @param ip IP地址
 */
export function validIP(ip: string): boolean {
  const reg = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
  return reg.test(ip)
}

/**
 * 验证端口号
 * @param port 端口号
 */
export function validPort(port: string | number): boolean {
  const portNum = typeof port === 'string' ? parseInt(port, 10) : port
  return portNum >= 1 && portNum <= 65535
}

/**
 * 验证中文
 * @param str 字符串
 */
export function validChinese(str: string): boolean {
  const reg = /^[\u4e00-\u9fa5]+$/
  return reg.test(str)
}

/**
 * 验证数字
 * @param str 字符串
 */
export function validNumber(str: string): boolean {
  const reg = /^[0-9]+$/
  return reg.test(str)
}

/**
 * 验证正整数
 * @param str 字符串
 */
export function validPositiveInteger(str: string): boolean {
  const reg = /^[1-9]\d*$/
  return reg.test(str)
}

/**
 * 验证非负整数
 * @param str 字符串
 */
export function validNonNegativeInteger(str: string): boolean {
  const reg = /^\d+$/
  return reg.test(str)
}

/**
 * 验证浮点数
 * @param str 字符串
 */
export function validFloat(str: string): boolean {
  const reg = /^-?\d+(\.\d+)?$/
  return reg.test(str)
}

/**
 * 验证正浮点数
 * @param str 字符串
 */
export function validPositiveFloat(str: string): boolean {
  const reg = /^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/
  return reg.test(str)
}
