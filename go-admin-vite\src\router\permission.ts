import router from './index'
import { useUserStore } from '@/stores/user'
import { usePermissionStore } from '@/stores/permission'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/404', '/401'] // 白名单

router.beforeEach(async (to, from, next) => {
  NProgress.start()
  
  const userStore = useUserStore()
  const permissionStore = usePermissionStore()
  
  if (userStore.token) {
    if (to.path === '/login') {
      // 如果已登录，重定向到首页
      next({ path: '/' })
      NProgress.done()
    } else {
      // 检查用户信息是否存在
      if (!userStore.userInfo) {
        try {
          // 获取用户信息
          await userStore.getUserInfo()
          
          // 生成可访问的路由表
          const accessRoutes = await permissionStore.generateRoutes(userStore.roles)
          console.log('生成的动态路由:', accessRoutes)

          // 清除之前的动态路由，避免重复
          const existingRoutes = router.getRoutes()
          existingRoutes.forEach(route => {
            // 只清除动态添加的路由，保留基础路由
            if (route.name && typeof route.name === 'string' &&
                !['Login', 'Dashboard', 'Profile', '404', '401'].includes(route.name)) {
              router.removeRoute(route.name)
            }
          })

          // 动态添加可访问路由
          accessRoutes.forEach(route => {
            console.log('添加路由:', route.path, route.name)
            router.addRoute(route)
          })

          // 打印当前所有路由
          console.log('当前所有路由:', router.getRoutes().map(r => ({ path: r.path, name: r.name })))

          // 确保添加路由已完成
          next({ ...to, replace: true })
        } catch (error) {
          // 获取用户信息失败，清除token并重定向到登录页
          await userStore.logout()
          ElMessage.error('获取用户信息失败，请重新登录')
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      } else {
        next()
      }
    }
  } else {
    // 没有token
    if (whiteList.includes(to.path)) {
      // 在免登录白名单，直接进入
      next()
    } else {
      // 其他没有访问权限的页面将重定向到登录页面
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})

// 动态设置页面标题
router.afterEach((to) => {
  const title = to.meta?.title as string
  if (title) {
    document.title = `${title} - Go Admin`
  } else {
    document.title = 'Go Admin'
  }
})
