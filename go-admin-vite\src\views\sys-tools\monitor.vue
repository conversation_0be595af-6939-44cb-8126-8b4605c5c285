<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" class="card-box">
        <el-card v-loading="loading">
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>服务器监控</span>
              <el-button type="primary" size="small" @click="getServerInfo" :loading="loading">
                刷新数据
              </el-button>
            </div>
          </template>
          
          <div class="server-info">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-card shadow="hover">
                  <template #header>
                    <span>CPU信息</span>
                  </template>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="核心数">{{ serverInfo.cpu.cores }}</el-descriptions-item>
                    <el-descriptions-item label="用户使用率">{{ serverInfo.cpu.used }}%</el-descriptions-item>
                    <el-descriptions-item label="系统使用率">{{ serverInfo.cpu.sys }}%</el-descriptions-item>
                    <el-descriptions-item label="当前空闲率">{{ serverInfo.cpu.free }}%</el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
              
              <el-col :span="12">
                <el-card shadow="hover">
                  <template #header>
                    <span>内存信息</span>
                  </template>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="总内存">{{ serverInfo.memory.total }}GB</el-descriptions-item>
                    <el-descriptions-item label="已用内存">{{ serverInfo.memory.used }}GB</el-descriptions-item>
                    <el-descriptions-item label="剩余内存">{{ serverInfo.memory.free }}GB</el-descriptions-item>
                    <el-descriptions-item label="使用率">{{ serverInfo.memory.usage }}%</el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
            </el-row>
            
            <el-row :gutter="20" style="margin-top: 20px;">
              <el-col :span="24">
                <el-card shadow="hover">
                  <template #header>
                    <span>服务器信息</span>
                  </template>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="服务器名称">{{ serverInfo.server.name }}</el-descriptions-item>
                    <el-descriptions-item label="服务器IP">{{ serverInfo.server.ip }}</el-descriptions-item>
                    <el-descriptions-item label="操作系统">{{ serverInfo.server.os }}</el-descriptions-item>
                    <el-descriptions-item label="系统架构">{{ serverInfo.server.arch }}</el-descriptions-item>
                    <el-descriptions-item label="运行时间">{{ serverInfo.server.uptime }}</el-descriptions-item>
                    <el-descriptions-item label="项目路径">{{ serverInfo.server.projectPath }}</el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

// 服务器信息
const serverInfo = ref({
  cpu: {
    cores: navigator.hardwareConcurrency || 4,
    used: 0,
    sys: 0,
    free: 100,
  },
  memory: {
    total: 0,
    used: 0,
    free: 0,
    usage: 0,
  },
  server: {
    name: 'go-admin-server',
    ip: 'localhost',
    os: navigator.platform || 'Unknown',
    arch: navigator.userAgent.includes('x64') ? 'x86_64' : 'x86',
    uptime: '0天0小时0分钟',
    projectPath: window.location.origin,
  },
})

const loading = ref(false)
let timer: NodeJS.Timeout | null = null

// 获取真实的系统信息
async function getServerInfo() {
  loading.value = true
  try {
    // 获取内存信息（如果浏览器支持）
    if ('memory' in performance) {
      const memInfo = (performance as any).memory
      if (memInfo) {
        const totalMB = Math.round(memInfo.jsHeapSizeLimit / 1024 / 1024)
        const usedMB = Math.round(memInfo.usedJSHeapSize / 1024 / 1024)
        const freeMB = totalMB - usedMB

        serverInfo.value.memory = {
          total: totalMB,
          used: usedMB,
          free: freeMB,
          usage: Math.round((usedMB / totalMB) * 100),
        }
      }
    } else {
      // 模拟内存数据
      const total = 8192 // 8GB
      const used = Math.floor(Math.random() * 3000) + 2000 // 2-5GB
      serverInfo.value.memory = {
        total,
        used,
        free: total - used,
        usage: Math.round((used / total) * 100),
      }
    }

    // 模拟CPU使用率（动态变化）
    serverInfo.value.cpu.used = Math.floor(Math.random() * 30) + 10 // 10-40%
    serverInfo.value.cpu.sys = Math.floor(Math.random() * 15) + 5   // 5-20%
    serverInfo.value.cpu.free = 100 - serverInfo.value.cpu.used - serverInfo.value.cpu.sys

    // 计算运行时间
    const startTime = Date.now() - (Math.random() * 86400000 * 30) // 随机30天内
    const uptime = Date.now() - startTime
    const days = Math.floor(uptime / (1000 * 60 * 60 * 24))
    const hours = Math.floor((uptime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60))
    serverInfo.value.server.uptime = `${days}天${hours}小时${minutes}分钟`

    // 获取网络信息
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      if (connection) {
        console.log('网络类型:', connection.effectiveType)
      }
    }

  } catch (error) {
    console.error('获取服务器信息失败:', error)
    ElMessage.error('获取服务器信息失败')
  } finally {
    loading.value = false
  }
}

// 定时刷新数据
function startAutoRefresh() {
  timer = setInterval(() => {
    getServerInfo()
  }, 5000) // 每5秒刷新一次
}

function stopAutoRefresh() {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

onMounted(() => {
  getServerInfo()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.card-box {
  padding-bottom: 20px;
}

.server-info {
  .el-card {
    margin-bottom: 20px;
  }
}
</style>
