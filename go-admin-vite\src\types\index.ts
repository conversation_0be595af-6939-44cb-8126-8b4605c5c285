// 用户相关类型
export interface User {
  userId: number
  username: string
  nickName: string
  email: string
  phone: string
  sex: string
  avatar: string
  status: string
  deptId: number
  postIds: number[]
  roleIds: number[]
  remark: string
  createTime: string
  updateTime: string
}

// 角色相关类型
export interface Role {
  roleId: number
  roleName: string
  roleKey: string
  roleSort: number
  status: string
  menuIds: number[]
  remark: string
  createTime: string
  updateTime: string
}

// 菜单相关类型
export interface Menu {
  menuId: number
  menuName: string
  parentId: number
  orderNum: number
  path: string
  component: string
  isFrame: string
  isCache: string
  menuType: string
  visible: string
  status: string
  perms: string
  icon: string
  remark: string
  createTime: string
  updateTime: string
  children?: Menu[]
}

// 部门相关类型
export interface Dept {
  deptId: number
  parentId: number
  deptName: string
  orderNum: number
  leader: string
  phone: string
  email: string
  status: string
  createTime: string
  updateTime: string
  children?: Dept[]
}

// 字典相关类型
export interface DictType {
  dictId: number
  dictName: string
  dictType: string
  status: string
  remark: string
  createTime: string
  updateTime: string
}

export interface DictData {
  dictCode: number
  dictSort: number
  dictLabel: string
  dictValue: string
  dictType: string
  cssClass: string
  listClass: string
  isDefault: string
  status: string
  remark: string
  createTime: string
  updateTime: string
}

// API 响应类型
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

export interface PageResult<T = any> {
  total: number
  rows: T[]
}

// 分页参数
export interface PageParams {
  pageNum: number
  pageSize: number
}

// 查询参数基类
export interface BaseQuery extends PageParams {
  [key: string]: any
}

// 登录相关类型
export interface LoginForm {
  username: string
  password: string
  code: string
  uuid: string
}

export interface LoginResult {
  token: string
  expire: string
}

// 路由相关类型
export interface RouteConfig {
  path: string
  name?: string
  component?: any
  redirect?: string
  meta?: RouteMeta
  children?: RouteConfig[]
}

export interface RouteMeta {
  title: string
  icon?: string
  hidden?: boolean
  roles?: string[]
  noCache?: boolean
  affix?: boolean
  breadcrumb?: boolean
  activeMenu?: string
}

// 系统配置类型
export interface SysConfig {
  configId: number
  configName: string
  configKey: string
  configValue: string
  configType: string
  remark: string
  createTime: string
  updateTime: string
}

// 操作日志类型
export interface OperLog {
  operId: number
  title: string
  businessType: number
  method: string
  requestMethod: string
  operatorType: number
  operName: string
  deptName: string
  operUrl: string
  operIp: string
  operLocation: string
  operParam: string
  jsonResult: string
  status: number
  errorMsg: string
  operTime: string
}

// 登录日志类型
export interface LoginLog {
  infoId: number
  userName: string
  ipaddr: string
  loginLocation: string
  browser: string
  os: string
  status: string
  msg: string
  loginTime: string
}

// 岗位类型
export interface Post {
  postId: number
  postCode: string
  postName: string
  postSort: number
  status: string
  remark: string
  createTime: string
  updateTime: string
}

// 定时任务类型
export interface Job {
  jobId: number
  jobName: string
  jobGroup: string
  invokeTarget: string
  cronExpression: string
  misfirePolicy: string
  concurrent: string
  status: string
  remark: string
  createTime: string
  updateTime: string
}

// 表单组件类型
export interface FormItem {
  label: string
  prop: string
  type: 'input' | 'select' | 'date' | 'textarea' | 'number' | 'switch'
  placeholder?: string
  options?: { label: string; value: any }[]
  rules?: any[]
  span?: number
}

// 表格列类型
export interface TableColumn {
  prop: string
  label: string
  width?: string | number
  minWidth?: string | number
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any) => string
  type?: 'selection' | 'index' | 'expand'
}
