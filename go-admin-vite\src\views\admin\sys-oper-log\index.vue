<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>操作日志</span>
        </div>
      </template>
      
      <!-- 查询条件 -->
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
        <el-form-item label="系统模块" prop="title">
          <el-input
            v-model="queryParams.title"
            placeholder="请输入系统模块"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="操作人员" prop="operName">
          <el-input
            v-model="queryParams.operName"
            placeholder="请输入操作人员"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="类型" prop="businessType">
          <el-select v-model="queryParams.businessType" placeholder="操作类型" clearable style="width: 200px">
            <el-option label="新增" value="0" />
            <el-option label="修改" value="1" />
            <el-option label="删除" value="2" />
            <el-option label="授权" value="3" />
            <el-option label="导出" value="4" />
            <el-option label="导入" value="5" />
            <el-option label="强退" value="6" />
            <el-option label="生成代码" value="7" />
            <el-option label="清空数据" value="8" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="操作状态" clearable style="width: 200px">
            <el-option label="成功" value="0" />
            <el-option label="失败" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
          >
            删除
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            @click="handleClean"
          >
            清空
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
          >
            导出
          </el-button>
        </el-col>
      </el-row>

      <!-- 操作日志表格 -->
      <el-table
        v-loading="loading"
        :data="logList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="日志编号" align="center" prop="operId" />
        <el-table-column label="系统模块" align="center" prop="title" />
        <el-table-column label="操作类型" align="center" prop="businessType">
          <template #default="scope">
            <el-tag :type="getBusinessTypeTag(scope.row.businessType)">
              {{ getBusinessTypeName(scope.row.businessType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作人员" align="center" prop="operName" />
        <el-table-column label="主机" align="center" prop="operIp" />
        <el-table-column label="操作地点" align="center" prop="operLocation" />
        <el-table-column label="操作状态" align="center" prop="status">
          <template #default="scope">
            <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
              {{ scope.row.status === '0' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作日期" align="center" prop="operTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.operTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button
              type="primary"
              icon="View"
              @click="handleView(scope.row)"
              link
            >
              详细
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination/index.vue'

// 模拟数据
const loading = ref(false)
const logList = ref([
  {
    operId: 1,
    title: '用户管理',
    businessType: '1',
    operName: 'admin',
    operIp: '127.0.0.1',
    operLocation: '内网IP',
    status: '0',
    operTime: '2023-12-01 10:30:00',
  },
  {
    operId: 2,
    title: '角色管理',
    businessType: '0',
    operName: 'admin',
    operIp: '127.0.0.1',
    operLocation: '内网IP',
    status: '0',
    operTime: '2023-12-01 09:15:00',
  },
])

const total = ref(2)
const multiple = ref(true)
const dateRange = ref([])

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  title: '',
  operName: '',
  businessType: '',
  status: '',
})

const queryFormRef = ref<FormInstance>()

function getList() {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 500)
}

function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

function resetQuery() {
  queryFormRef.value?.resetFields()
  dateRange.value = []
  handleQuery()
}

function handleSelectionChange(selection: any[]) {
  multiple.value = !selection.length
}

function handleDelete() {
  ElMessage.success('删除选中的操作日志')
}

function handleClean() {
  ElMessageBox.confirm(
    '确定要清空所有操作日志吗？',
    '清空确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success('清空成功')
  }).catch(() => {
    ElMessage.info('已取消清空')
  })
}

function handleExport() {
  ElMessage.success('正在导出操作日志...')
  setTimeout(() => {
    ElMessage.success('导出完成')
  }, 1000)
}

function handleView(row: any) {
  ElMessage.info(`查看操作日志详情: ${row.title}`)
}

function getBusinessTypeName(type: string) {
  const types: Record<string, string> = {
    '0': '新增',
    '1': '修改',
    '2': '删除',
    '3': '授权',
    '4': '导出',
    '5': '导入',
    '6': '强退',
    '7': '生成代码',
    '8': '清空数据',
  }
  return types[type] || '其他'
}

function getBusinessTypeTag(type: string) {
  const tags: Record<string, string> = {
    '0': 'success',
    '1': 'warning',
    '2': 'danger',
    '3': 'info',
    '4': 'success',
    '5': 'warning',
    '6': 'danger',
    '7': 'info',
    '8': 'danger',
  }
  return tags[type] || ''
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
</style>
