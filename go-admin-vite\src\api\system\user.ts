import { request } from '@/utils/request'
import type { User, PageResult, BaseQuery } from '@/types'

export interface UserQuery extends BaseQuery {
  username?: string
  phone?: string
  status?: string
  deptId?: number
  beginTime?: string
  endTime?: string
}

/**
 * 查询用户列表
 */
export function listUser(query: UserQuery) {
  return request.get<PageResult<User>>('/system/user/list', { params: query })
}

/**
 * 查询用户详细
 */
export function getUser(userId: number) {
  return request.get<User>(`/system/user/${userId}`)
}

/**
 * 新增用户
 */
export function addUser(data: Partial<User>) {
  return request.post('/system/user', data)
}

/**
 * 修改用户
 */
export function updateUser(data: Partial<User>) {
  return request.put('/system/user', data)
}

/**
 * 删除用户
 */
export function delUser(userIds: number | number[]) {
  return request.delete(`/system/user/${userIds}`)
}

/**
 * 重置用户密码
 */
export function resetUserPwd(userId: number, password: string) {
  return request.put('/system/user/resetPwd', { userId, password })
}

/**
 * 用户状态修改
 */
export function changeUserStatus(userId: number, status: string) {
  return request.put('/system/user/changeStatus', { userId, status })
}

/**
 * 查询用户个人信息
 */
export function getUserProfile() {
  return request.get<User>('/system/user/profile')
}

/**
 * 修改用户个人信息
 */
export function updateUserProfile(data: Partial<User>) {
  return request.put('/system/user/profile', data)
}

/**
 * 用户密码重置
 */
export function updateUserPwd(oldPassword: string, newPassword: string) {
  return request.put('/system/user/profile/updatePwd', {
    oldPassword,
    newPassword,
  })
}

/**
 * 用户头像上传
 */
export function uploadAvatar(data: FormData) {
  return request.post<{ imgUrl: string }>('/system/user/profile/avatar', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
