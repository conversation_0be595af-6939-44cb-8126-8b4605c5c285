import { ElMessage } from 'element-plus'

/**
 * 解析时间
 * @param time 时间
 * @param pattern 格式
 */
export function parseTime(time?: string | number | Date, pattern = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!time) return ''
  
  const date = new Date(time)
  if (isNaN(date.getTime())) return ''
  
  const formatObj: Record<string, number> = {
    Y: date.getFullYear(),
    M: date.getMonth() + 1,
    D: date.getDate(),
    H: date.getHours(),
    m: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  }
  
  const timeStr = pattern.replace(/(Y+|M+|D+|H+|m+|s+|a)/g, (result, key) => {
    const value = formatObj[key[0]]
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(key.length, '0')
  })
  
  return timeStr
}

/**
 * 重置表单
 * @param refName 表单ref名称
 */
export function resetForm(refName: string) {
  const form = document.querySelector(`[ref="${refName}"]`) as any
  if (form && form.resetFields) {
    form.resetFields()
  }
}

/**
 * 添加日期范围
 * @param params 参数对象
 * @param dateRange 日期范围
 * @param propName 属性名前缀
 */
export function addDateRange(params: any, dateRange: string[], propName = 'Time'): any {
  const search = { ...params }
  if (dateRange && dateRange.length === 2) {
    search[`begin${propName}`] = dateRange[0]
    search[`end${propName}`] = dateRange[1]
  }
  return search
}

/**
 * 根据字典类型查询字典数据信息
 * @param dictType 字典类型
 * @param value 字典值
 */
export function selectDictLabel(dicts: any[], value: string): string {
  if (!value || !dicts || dicts.length === 0) return ''
  
  const dict = dicts.find(item => item.dictValue === value)
  return dict ? dict.dictLabel : value
}

/**
 * 根据字典类型查询字典数据信息
 * @param dicts 字典数据
 * @param value 字典值
 */
export function selectDictLabels(dicts: any[], value: string, separator = ','): string {
  if (!value || !dicts || dicts.length === 0) return ''
  
  const values = value.split(separator)
  const labels = values.map(val => {
    const dict = dicts.find(item => item.dictValue === val)
    return dict ? dict.dictLabel : val
  })
  
  return labels.join(separator)
}

/**
 * 回显数据字典
 * @param dicts 字典数据
 * @param value 字典值
 */
export function selectItemsLabel(items: any[], value: string): string {
  if (!value || !items || items.length === 0) return ''
  
  const item = items.find(item => item.value === value)
  return item ? item.label : value
}

/**
 * 通用下载方法
 * @param fileName 文件名
 * @param content 文件内容
 * @param contentType 文件类型
 */
export function download(fileName: string, content: BlobPart, contentType = 'application/octet-stream') {
  const blob = new Blob([content], { type: contentType })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = fileName
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

/**
 * 字符串格式化(%s )
 * @param str 字符串
 * @param args 参数
 */
export function sprintf(str: string, ...args: any[]): string {
  let i = 0
  return str.replace(/%s/g, () => args[i++])
}

/**
 * 构造树型结构数据
 * @param data 数据源
 * @param id id字段 默认 'id'
 * @param parentId 父节点字段 默认 'parentId'
 * @param children 孩子节点字段 默认 'children'
 */
export function handleTree<T extends Record<string, any>>(
  data: T[],
  id = 'id',
  parentId = 'parentId',
  children = 'children'
): T[] {
  const config = { id, parentId, childrenList: children }
  
  const childrenListMap: Record<string, T[]> = {}
  const nodeIds: Record<string, T> = {}
  const tree: T[] = []
  
  for (const d of data) {
    const parentIdValue = d[config.parentId]
    if (!childrenListMap[parentIdValue]) {
      childrenListMap[parentIdValue] = []
    }
    nodeIds[d[config.id]] = d
    childrenListMap[parentIdValue].push(d)
  }
  
  for (const d of data) {
    const parentIdValue = d[config.parentId]
    if (!nodeIds[parentIdValue]) {
      tree.push(d)
    }
  }
  
  for (const t of tree) {
    adaptToChildrenList(t)
  }
  
  function adaptToChildrenList(o: T) {
    if (childrenListMap[o[config.id]]) {
      o[config.childrenList] = childrenListMap[o[config.id]]
    }
    if (o[config.childrenList]) {
      for (const c of o[config.childrenList]) {
        adaptToChildrenList(c)
      }
    }
  }
  
  return tree
}

/**
 * 参数处理
 * @param params 参数
 */
export function tansParams(params: Record<string, any>): string {
  let result = ''
  for (const propName of Object.keys(params)) {
    const value = params[propName]
    if (value !== null && value !== '' && typeof value !== 'undefined') {
      const part = `${encodeURIComponent(propName)}=${encodeURIComponent(value)}`
      result += (result ? '&' : '') + part
    }
  }
  return result
}

/**
 * 验证是否为blob格式
 * @param data 数据
 */
export function blobValidate(data: any): boolean {
  return data.type !== 'application/json'
}

/**
 * 成功消息提示
 * @param msg 消息内容
 */
export function msgSuccess(msg: string) {
  ElMessage.success(msg)
}

/**
 * 错误消息提示
 * @param msg 消息内容
 */
export function msgError(msg: string) {
  ElMessage.error(msg)
}

/**
 * 警告消息提示
 * @param msg 消息内容
 */
export function msgWarning(msg: string) {
  ElMessage.warning(msg)
}

/**
 * 信息消息提示
 * @param msg 消息内容
 */
export function msgInfo(msg: string) {
  ElMessage.info(msg)
}

/**
 * 防抖函数
 * @param func 函数
 * @param wait 等待时间
 * @param immediate 是否立即执行
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return function (this: any, ...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func.apply(this, args)
    }
    
    const callNow = immediate && !timeout
    
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) func.apply(this, args)
  }
}

/**
 * 节流函数
 * @param func 函数
 * @param wait 等待时间
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let previous = 0
  
  return function (this: any, ...args: Parameters<T>) {
    const now = Date.now()
    if (now - previous > wait) {
      func.apply(this, args)
      previous = now
    }
  }
}
