import { request } from '@/utils/request'
import type { Menu, BaseQuery } from '@/types'

export interface MenuQuery extends BaseQuery {
  menuName?: string
  status?: string
}

/**
 * 查询菜单列表
 */
export function listMenu(query?: MenuQuery) {
  return request.get<Menu[]>('/system/menu/list', { params: query })
}

/**
 * 查询菜单详细
 */
export function getMenu(menuId: number) {
  return request.get<Menu>(`/system/menu/${menuId}`)
}

/**
 * 查询菜单下拉树结构
 */
export function treeselect() {
  return request.get<Menu[]>('/system/menu/treeselect')
}

/**
 * 根据角色ID查询菜单下拉树结构
 */
export function roleMenuTreeselect(roleId: number) {
  return request.get<{
    checkedKeys: number[]
    menus: Menu[]
  }>(`/system/menu/roleMenuTreeselect/${roleId}`)
}

/**
 * 新增菜单
 */
export function addMenu(data: Partial<Menu>) {
  return request.post('/system/menu', data)
}

/**
 * 修改菜单
 */
export function updateMenu(data: Partial<Menu>) {
  return request.put('/system/menu', data)
}

/**
 * 删除菜单
 */
export function delMenu(menuId: number) {
  return request.delete(`/system/menu/${menuId}`)
}

/**
 * 获取路由
 */
export function getRouters() {
  return request.get<Menu[]>('/system/menu/routes')
}
