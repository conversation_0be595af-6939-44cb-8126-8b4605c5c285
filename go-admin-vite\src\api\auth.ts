import { request } from '@/utils/request'
import type { LoginForm, LoginResult, User } from '@/types'

/**
 * 用户登录
 */
export function login(data: LoginForm) {
  return request.post<LoginResult>('/api/v1/login', data)
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return request.get<{
    user: User
    roles: string[]
    permissions: string[]
  }>('/api/v1/getinfo')
}

/**
 * 用户登出
 */
export function logout() {
  return request.post('/api/v1/logout')
}

/**
 * 获取验证码
 */
export function getCaptcha() {
  return request.get<any>('/api/v1/captcha')
}
