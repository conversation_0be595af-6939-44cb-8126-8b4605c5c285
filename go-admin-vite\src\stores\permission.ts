import { ref } from 'vue'
import { defineStore } from 'pinia'
import type { RouteRecordRaw } from 'vue-router'
import { request } from '@/utils/request'

// 导入静态路由
import { constantRoutes } from '@/router'

export const usePermissionStore = defineStore('permission', () => {
  const routes = ref<RouteRecordRaw[]>(constantRoutes)
  const addRoutes = ref<RouteRecordRaw[]>([])
  const sidebarRoutes = ref<RouteRecordRaw[]>(constantRoutes)

  // 生成路由
  async function generateRoutes(roles: string[]): Promise<RouteRecordRaw[]> {
    try {
      console.log('生成路由，用户角色:', roles)

      // 获取后端菜单数据
      const menuData = await request.get<any[]>('/api/v1/menurole')
      console.log('获取到的菜单数据:', menuData)

      // 将后端菜单数据转换为前端路由格式
      const accessedRoutes = generateMenuRoutes(menuData)

      addRoutes.value = accessedRoutes
      routes.value = constantRoutes.concat(accessedRoutes)
      sidebarRoutes.value = routes.value

      return accessedRoutes
    } catch (error) {
      console.error('Generate routes error:', error)
      // 如果获取菜单失败，返回空数组，使用常量路由
      return []
    }
  }

  // 重置路由
  function resetRoutes(): void {
    routes.value = []
    addRoutes.value = []
    sidebarRoutes.value = []
  }

  return {
    routes,
    addRoutes,
    sidebarRoutes,
    generateRoutes,
    resetRoutes,
  }
})

// 菜单路由生成函数
function generateMenuRoutes(menuData: any[]): RouteRecordRaw[] {
  const routes: RouteRecordRaw[] = []

  menuData.forEach(menu => {
    const route = convertMenuToRoute(menu)
    if (route) {
      routes.push(route)
    }
  })

  return routes
}

// 将菜单数据转换为路由
function convertMenuToRoute(menu: any): RouteRecordRaw | null {
  if (!menu) return null

  // 只处理目录(M)和菜单(C)类型，跳过按钮(F)类型
  if (menu.menuType === 'F') {
    return null
  }

  // 如果是Layout组件，需要特殊处理
  let component
  if (menu.component === 'Layout' || menu.menuType === 'M') {
    component = () => import('@/layouts/index.vue')
  } else {
    component = getComponent(menu.component)
  }

  const route: any = {
    path: menu.path || '/',
    name: menu.menuName || `Menu${menu.menuId}`,
    component: component,
    meta: {
      title: menu.title || menu.menuName,
      icon: menu.icon,
      hidden: menu.visible !== '0',
      noCache: !menu.noCache,
    },
  }

  // 处理子菜单
  if (menu.children && menu.children.length > 0) {
    const children: RouteRecordRaw[] = []
    menu.children.forEach((child: any) => {
      const childRoute = convertMenuToRoute(child)
      if (childRoute) {
        children.push(childRoute)
      }
    })

    if (children.length > 0) {
      route.children = children
      // 如果有子路由但没有redirect，设置默认redirect
      if (!route.redirect) {
        const firstVisibleChild = children.find(child => !child.meta?.hidden)
        if (firstVisibleChild) {
          route.redirect = firstVisibleChild.path
        }
      }
    }
  }

  return route
}

// 获取组件
function getComponent(componentPath: string) {
  if (!componentPath || componentPath === '#' || componentPath === 'Layout') {
    return () => import('@/layouts/components/RouterView.vue')
  }

  // 清理路径，移除开头的斜杠和多余的斜杠
  let cleanPath = componentPath.replace(/^\/+/, '').replace(/\/+/g, '/')

  console.log('动态导入组件:', cleanPath)

  // 使用静态的组件映射，避免 Vite 动态导入警告
  const componentMap: Record<string, () => Promise<any>> = {
    // 系统管理
    'admin/sys-user/index': () => import('@/views/admin/sys-user/index.vue'),
    'admin/sys-role/index': () => import('@/views/admin/sys-role/index.vue'),
    'admin/sys-menu/index': () => import('@/views/admin/sys-menu/index.vue'),
    'admin/sys-dept/index': () => import('@/views/admin/sys-dept/index.vue'),
    'admin/sys-post/index': () => import('@/views/admin/sys-post/index.vue'),
    'admin/dict/index': () => import('@/views/admin/dict/index.vue'),
    'admin/sys-config/index': () => import('@/views/admin/sys-config/index.vue'),
    'admin/sys-login-log/index': () => import('@/views/admin/sys-login-log/index.vue'),
    'admin/sys-oper-log/index': () => import('@/views/admin/sys-oper-log/index.vue'),

    // 开发工具
    'dev-tools/swagger/index': () => import('@/views/dev-tools/swagger/index.vue'),
    'dev-tools/build/index': () => import('@/views/dev-tools/build/index.vue'),
    'dev-tools/gen/index': () => import('@/views/dev-tools/gen/index.vue'),

    // 系统工具
    'sys-tools/monitor': () => import('@/views/sys-tools/monitor.vue'),
  }

  // 尝试从组件映射中获取
  const componentLoader = componentMap[cleanPath] || componentMap[cleanPath.replace('.vue', '')]

  if (componentLoader) {
    return componentLoader
  }

  // 如果找不到，返回404页面
  console.warn(`组件 ${cleanPath} 不存在，使用404页面`)
  return () => import('@/views/error/404.vue').catch(() => {
    // 如果连404页面都不存在，返回一个简单的组件
    return {
      default: {
        template: `
          <div class="app-container">
            <el-card>
              <div style="text-align: center; padding: 50px;">
                <h3>页面不存在</h3>
                <p>组件路径: ${cleanPath}</p>
                <p>请联系管理员检查菜单配置</p>
              </div>
            </el-card>
          </div>
        `
      }
    }
  })
}
