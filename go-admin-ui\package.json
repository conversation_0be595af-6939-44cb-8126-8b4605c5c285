{"name": "go-admin", "version": "2.0.9", "description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "author": "https://github.com/wenjianzhang", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "new": "plop"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "go-admin", "go-admin-ui", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "git+https://github.com/go-admin-team/go-admin.git"}, "bugs": {"url": "https://github.com/go-admin-team/go-admin/issues"}, "dependencies": {"@riophae/vue-treeselect": "0.4.0", "awe-dnd": "^0.3.4", "axios": "0.21.1", "clipboard": "2.0.6", "codemirror": "5.62.0", "core-js": "^3.6.5", "driver.js": "0.9.8", "dropzone": "5.7.2", "echarts": "4.8.0", "element-ui": "2.15.6", "file-saver": "2.0.2", "fuse.js": "6.4.1", "js-cookie": "2.2.1", "jsonlint": "1.6.3", "jszip": "3.5.0", "moment": "^2.27.0", "normalize.css": "8.0.1", "nprogress": "0.2.0", "path-to-regexp": "6.1.0", "remixicon": "^2.5.0", "sass-resources-loader": "^2.0.3", "screenfull": "5.0.2", "viser-vue": "^2.4.8", "vue": "2.6.11", "vue-codemirror": "^4.0.6", "vue-count-to": "1.0.13", "vue-cropper": "^0.5.5", "vue-particles": "^1.0.9", "vue-router": "3.4.7", "vuedraggable": "2.24.0", "vuex": "3.5.1", "webpack-bundle-analyzer": "^3.8.0", "xlsx": "0.16.5"}, "devDependencies": {"@babel/core": "7.11.1", "@babel/register": "^7.10.5", "@babel/runtime": "^7.12.1", "@vue/babel-preset-app": "^4.5.7", "@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "^4.4.6", "@vue/cli-plugin-unit-jest": "4.4.6", "@vue/cli-service": "^4.5.13", "@vue/test-utils": "1.0.3", "autoprefixer": "^9.8.6", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.1.0", "babel-jest": "26.2.2", "babel-plugin-dynamic-import-node": "^2.3.3", "beautifier": "^0.1.7", "chalk": "4.1.0", "chokidar": "3.4.2", "compression-webpack-plugin": "^4.0.0", "connect": "3.7.0", "eslint": "7.6.0", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "4.3.0", "husky": "4.2.5", "lint-staged": "10.2.11", "mockjs": "1.1.0", "plop": "2.7.4", "runjs": "^4.4.2", "sass": "^1.35.1", "sass-loader": "^9.0.3", "script-ext-html-webpack-plugin": "2.1.4", "script-loader": "0.7.2", "serve-static": "^1.14.1", "svg-sprite-loader": "^5.0.0", "svgo": "1.3.2", "vue-template-compiler": "2.6.11"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}