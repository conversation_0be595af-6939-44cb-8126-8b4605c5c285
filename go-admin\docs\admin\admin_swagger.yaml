definitions:
  dto.GetSetSysConfigReq:
    properties:
      configKey:
        type: string
      configValue:
        type: string
    type: object
  dto.PassWord:
    properties:
      newPassword:
        type: string
      oldPassword:
        type: string
    type: object
  dto.ResetSysUserPwdReq:
    properties:
      createBy:
        type: integer
      password:
        type: string
      updateBy:
        type: integer
      userId:
        description: 用户ID
        type: integer
    type: object
  dto.RoleDataScopeReq:
    properties:
      dataScope:
        type: string
      deptIds:
        items:
          type: integer
        type: array
      roleId:
        type: integer
    required:
    - dataScope
    - roleId
    type: object
  dto.SysApiDeleteReq:
    properties:
      ids:
        items:
          type: integer
        type: array
    type: object
  dto.SysApiUpdateReq:
    properties:
      action:
        type: string
      createBy:
        type: integer
      handle:
        type: string
      id:
        description: 编码
        type: integer
      path:
        type: string
      title:
        type: string
      type:
        type: string
      updateBy:
        type: integer
    type: object
  dto.SysConfigByKeyReq:
    properties:
      configKey:
        type: string
    type: object
  dto.SysConfigControl:
    properties:
      configKey:
        type: string
      configName:
        type: string
      configType:
        type: string
      configValue:
        type: string
      createBy:
        type: integer
      id:
        description: 编码
        type: integer
      isFrontend:
        type: string
      remark:
        type: string
      updateBy:
        type: integer
    type: object
  dto.SysDeptDeleteReq:
    properties:
      ids:
        items:
          type: integer
        type: array
    type: object
  dto.SysDeptInsertReq:
    properties:
      createBy:
        type: integer
      deptId:
        description: 编码
        type: integer
      deptName:
        description: 部门名称
        type: string
      deptPath:
        description: 路径
        type: string
      email:
        description: 邮箱
        type: string
      leader:
        description: 负责人
        type: string
      parentId:
        description: 上级部门
        type: integer
      phone:
        description: 手机
        type: string
      sort:
        description: 排序
        type: integer
      status:
        description: 状态
        type: integer
      updateBy:
        type: integer
    type: object
  dto.SysDeptUpdateReq:
    properties:
      createBy:
        type: integer
      deptId:
        description: 编码
        type: integer
      deptName:
        description: 部门名称
        type: string
      deptPath:
        description: 路径
        type: string
      email:
        description: 邮箱
        type: string
      leader:
        description: 负责人
        type: string
      parentId:
        description: 上级部门
        type: integer
      phone:
        description: 手机
        type: string
      sort:
        description: 排序
        type: integer
      status:
        description: 状态
        type: integer
      updateBy:
        type: integer
    type: object
  dto.SysDictDataDeleteReq:
    properties:
      createBy:
        type: integer
      ids:
        items:
          type: integer
        type: array
      updateBy:
        type: integer
    type: object
  dto.SysDictDataGetAllResp:
    properties:
      label:
        type: string
      value:
        type: string
    type: object
  dto.SysDictDataInsertReq:
    properties:
      createBy:
        type: integer
      cssClass:
        type: string
      default:
        type: string
      dictLabel:
        type: string
      dictSort:
        type: integer
      dictType:
        type: string
      dictValue:
        type: string
      isDefault:
        type: string
      listClass:
        type: string
      remark:
        type: string
      status:
        type: integer
      updateBy:
        type: integer
    type: object
  dto.SysDictDataUpdateReq:
    properties:
      createBy:
        type: integer
      cssClass:
        type: string
      default:
        type: string
      dictLabel:
        type: string
      dictSort:
        type: integer
      dictType:
        type: string
      dictValue:
        type: string
      id:
        type: integer
      isDefault:
        type: string
      listClass:
        type: string
      remark:
        type: string
      status:
        type: integer
      updateBy:
        type: integer
    type: object
  dto.SysDictTypeDeleteReq:
    properties:
      createBy:
        type: integer
      ids:
        items:
          type: integer
        type: array
      updateBy:
        type: integer
    type: object
  dto.SysDictTypeInsertReq:
    properties:
      createBy:
        type: integer
      dictName:
        type: string
      dictType:
        type: string
      id:
        type: integer
      remark:
        type: string
      status:
        type: integer
      updateBy:
        type: integer
    type: object
  dto.SysDictTypeUpdateReq:
    properties:
      createBy:
        type: integer
      dictName:
        type: string
      dictType:
        type: string
      id:
        type: integer
      remark:
        type: string
      status:
        type: integer
      updateBy:
        type: integer
    type: object
  dto.SysLoginLogDeleteReq:
    properties:
      ids:
        items:
          type: integer
        type: array
    type: object
  dto.SysMenuDeleteReq:
    properties:
      createBy:
        type: integer
      ids:
        items:
          type: integer
        type: array
      updateBy:
        type: integer
    type: object
  dto.SysMenuInsertReq:
    properties:
      action:
        description: 请求方式
        type: string
      apis:
        items:
          type: integer
        type: array
      breadcrumb:
        description: 是否面包屑
        type: string
      component:
        description: 组件
        type: string
      createBy:
        type: integer
      icon:
        description: 图标
        type: string
      isFrame:
        description: 是否frame
        type: string
      menuId:
        description: 编码
        type: integer
      menuName:
        description: 菜单name
        type: string
      menuType:
        description: 菜单类型
        type: string
      noCache:
        description: 是否缓存
        type: boolean
      parentId:
        description: 上级菜单
        type: integer
      path:
        description: 路径
        type: string
      paths:
        description: id路径
        type: string
      permission:
        description: 权限编码
        type: string
      sort:
        description: 排序
        type: integer
      sysApi:
        items:
          $ref: '#/definitions/models.SysApi'
        type: array
      title:
        description: 显示名称
        type: string
      updateBy:
        type: integer
      visible:
        description: 是否显示
        type: string
    type: object
  dto.SysMenuUpdateReq:
    properties:
      action:
        description: 请求方式
        type: string
      apis:
        items:
          type: integer
        type: array
      breadcrumb:
        description: 是否面包屑
        type: string
      component:
        description: 组件
        type: string
      createBy:
        type: integer
      icon:
        description: 图标
        type: string
      isFrame:
        description: 是否frame
        type: string
      menuId:
        description: 编码
        type: integer
      menuName:
        description: 菜单name
        type: string
      menuType:
        description: 菜单类型
        type: string
      noCache:
        description: 是否缓存
        type: boolean
      parentId:
        description: 上级菜单
        type: integer
      path:
        description: 路径
        type: string
      paths:
        description: id路径
        type: string
      permission:
        description: 权限编码
        type: string
      sort:
        description: 排序
        type: integer
      sysApi:
        items:
          $ref: '#/definitions/models.SysApi'
        type: array
      title:
        description: 显示名称
        type: string
      updateBy:
        type: integer
      visible:
        description: 是否显示
        type: string
    type: object
  dto.SysOperaLogDeleteReq:
    properties:
      ids:
        items:
          type: integer
        type: array
    type: object
  dto.SysPostDeleteReq:
    properties:
      createBy:
        type: integer
      ids:
        items:
          type: integer
        type: array
      updateBy:
        type: integer
    type: object
  dto.SysPostInsertReq:
    properties:
      createBy:
        type: integer
      postCode:
        type: string
      postId:
        type: integer
      postName:
        type: string
      remark:
        type: string
      sort:
        type: integer
      status:
        type: integer
      updateBy:
        type: integer
    type: object
  dto.SysPostUpdateReq:
    properties:
      createBy:
        type: integer
      postCode:
        type: string
      postId:
        type: integer
      postName:
        type: string
      remark:
        type: string
      sort:
        type: integer
      status:
        type: integer
      updateBy:
        type: integer
    type: object
  dto.SysRoleDeleteReq:
    properties:
      ids:
        items:
          type: integer
        type: array
    type: object
  dto.SysRoleInsertReq:
    properties:
      admin:
        type: boolean
      createBy:
        type: integer
      dataScope:
        type: string
      deptIds:
        items:
          type: integer
        type: array
      flag:
        description: 标记
        type: string
      menuIds:
        items:
          type: integer
        type: array
      remark:
        description: 备注
        type: string
      roleId:
        description: 角色编码
        type: integer
      roleKey:
        description: 角色代码
        type: string
      roleName:
        description: 角色名称
        type: string
      roleSort:
        description: 角色排序
        type: integer
      status:
        description: 状态 1禁用 2正常
        type: string
      sysDept:
        items:
          $ref: '#/definitions/models.SysDept'
        type: array
      sysMenu:
        items:
          $ref: '#/definitions/models.SysMenu'
        type: array
      updateBy:
        type: integer
    type: object
  dto.SysRoleUpdateReq:
    properties:
      admin:
        type: boolean
      createBy:
        type: integer
      dataScope:
        type: string
      deptIds:
        items:
          type: integer
        type: array
      flag:
        description: 标记
        type: string
      menuIds:
        items:
          type: integer
        type: array
      remark:
        description: 备注
        type: string
      roleId:
        description: 角色编码
        type: integer
      roleKey:
        description: 角色代码
        type: string
      roleName:
        description: 角色名称
        type: string
      roleSort:
        description: 角色排序
        type: integer
      status:
        description: 状态
        type: string
      sysDept:
        items:
          $ref: '#/definitions/models.SysDept'
        type: array
      sysMenu:
        items:
          $ref: '#/definitions/models.SysMenu'
        type: array
      updateBy:
        type: integer
    type: object
  dto.SysUserInsertReq:
    properties:
      avatar:
        type: string
      createBy:
        type: integer
      deptId:
        type: integer
      email:
        type: string
      nickName:
        type: string
      password:
        type: string
      phone:
        type: string
      postId:
        type: integer
      remark:
        type: string
      roleId:
        type: integer
      sex:
        type: string
      status:
        default: "1"
        type: string
      updateBy:
        type: integer
      userId:
        description: 用户ID
        type: integer
      username:
        type: string
    type: object
  dto.SysUserUpdateReq:
    properties:
      avatar:
        type: string
      createBy:
        type: integer
      deptId:
        type: integer
      email:
        type: string
      nickName:
        type: string
      phone:
        type: string
      postId:
        type: integer
      remark:
        type: string
      roleId:
        type: integer
      sex:
        type: string
      status:
        default: "1"
        type: string
      updateBy:
        type: integer
      userId:
        description: 用户ID
        type: integer
      username:
        type: string
    type: object
  dto.UpdateStatusReq:
    properties:
      createBy:
        type: integer
      roleId:
        description: 角色编码
        type: integer
      status:
        description: 状态
        type: string
      updateBy:
        type: integer
    type: object
  dto.UpdateSysUserStatusReq:
    properties:
      createBy:
        type: integer
      status:
        type: string
      updateBy:
        type: integer
      userId:
        description: 用户ID
        type: integer
    type: object
  handler.Login:
    properties:
      code:
        type: string
      password:
        type: string
      username:
        type: string
      uuid:
        type: string
    required:
    - code
    - password
    - username
    - uuid
    type: object
  models.SysApi:
    properties:
      action:
        type: string
      createBy:
        type: integer
      createdAt:
        type: string
      handle:
        type: string
      id:
        type: integer
      path:
        type: string
      title:
        type: string
      type:
        type: string
      updateBy:
        type: integer
      updatedAt:
        type: string
    type: object
  models.SysConfig:
    properties:
      configKey:
        type: string
      configName:
        type: string
      configType:
        type: string
      configValue:
        type: string
      createBy:
        type: integer
      createdAt:
        type: string
      id:
        type: integer
      isFrontend:
        type: string
      remark:
        type: string
      updateBy:
        type: integer
      updatedAt:
        type: string
    type: object
  models.SysDept:
    properties:
      children:
        items:
          $ref: '#/definitions/models.SysDept'
        type: array
      createBy:
        type: integer
      createdAt:
        type: string
      dataScope:
        type: string
      deptId:
        description: 部门编码
        type: integer
      deptName:
        description: 部门名称
        type: string
      deptPath:
        type: string
      email:
        description: 邮箱
        type: string
      leader:
        description: 负责人
        type: string
      params:
        type: string
      parentId:
        description: 上级部门
        type: integer
      phone:
        description: 手机
        type: string
      sort:
        description: 排序
        type: integer
      status:
        description: 状态
        type: integer
      updateBy:
        type: integer
      updatedAt:
        type: string
    type: object
  models.SysMenu:
    properties:
      action:
        type: string
      apis:
        items:
          type: integer
        type: array
      breadcrumb:
        type: string
      children:
        items:
          $ref: '#/definitions/models.SysMenu'
        type: array
      component:
        type: string
      createBy:
        type: integer
      createdAt:
        type: string
      dataScope:
        type: string
      icon:
        type: string
      is_select:
        type: boolean
      isFrame:
        type: string
      menuId:
        type: integer
      menuName:
        type: string
      menuType:
        type: string
      noCache:
        type: boolean
      params:
        type: string
      parentId:
        type: integer
      path:
        type: string
      paths:
        type: string
      permission:
        type: string
      roleId:
        type: integer
      sort:
        type: integer
      sysApi:
        items:
          $ref: '#/definitions/models.SysApi'
        type: array
      title:
        type: string
      updateBy:
        type: integer
      updatedAt:
        type: string
      visible:
        type: string
    type: object
  response.Page:
    properties:
      count:
        type: integer
      pageIndex:
        type: integer
      pageSize:
        type: integer
    type: object
  response.Response:
    properties:
      code:
        type: integer
      msg:
        type: string
      requestId:
        description: 数据集
        type: string
      status:
        type: string
    type: object
  tools.Params:
    properties:
      treeCode:
        type: string
      treeName:
        type: string
      treeParentCode:
        type: string
    type: object
  tools.SysColumns:
    properties:
      columnComment:
        type: string
      columnId:
        type: integer
      columnName:
        type: string
      columnType:
        type: string
      createBy:
        type: integer
      createdAt:
        type: string
      deletedAt:
        type: string
      dictType:
        type: string
      edit:
        type: boolean
      fkCol:
        items:
          $ref: '#/definitions/tools.SysColumns'
        type: array
      fkLabelId:
        type: string
      fkLabelName:
        type: string
      fkTableName:
        type: string
      fkTableNameClass:
        type: string
      fkTableNamePackage:
        type: string
      goField:
        type: string
      goType:
        type: string
      htmlType:
        type: string
      increment:
        type: boolean
      insert:
        type: boolean
      isEdit:
        type: string
      isIncrement:
        type: string
      isInsert:
        type: string
      isList:
        type: string
      isPk:
        type: string
      isQuery:
        type: string
      isRequired:
        type: string
      jsonField:
        type: string
      list:
        type: string
      pk:
        type: boolean
      query:
        type: boolean
      queryType:
        type: string
      remark:
        type: string
      required:
        type: boolean
      sort:
        type: integer
      superColumn:
        type: boolean
      tableId:
        type: integer
      updateBy:
        type: integer
      updatedAt:
        type: string
      usableColumn:
        type: boolean
    type: object
  tools.SysTables:
    properties:
      businessName:
        type: string
      className:
        description: 类名
        type: string
      columns:
        items:
          $ref: '#/definitions/tools.SysColumns'
        type: array
      createBy:
        type: integer
      createdAt:
        type: string
      crud:
        type: boolean
      dataScope:
        type: string
      deletedAt:
        type: string
      functionAuthor:
        description: 功能作者
        type: string
      functionName:
        description: 功能名称
        type: string
      isActions:
        type: integer
      isAuth:
        type: integer
      isDataScope:
        type: integer
      isLogicalDelete:
        type: string
      logicalDelete:
        type: boolean
      logicalDeleteColumn:
        type: string
      moduleFrontName:
        description: 前端文件名
        type: string
      moduleName:
        description: go文件名
        type: string
      options:
        type: string
      packageName:
        description: 包名
        type: string
      params:
        $ref: '#/definitions/tools.Params'
      pkColumn:
        type: string
      pkGoField:
        type: string
      pkJsonField:
        type: string
      remark:
        type: string
      tableComment:
        description: 表备注
        type: string
      tableId:
        description: 表编码
        type: integer
      tableName:
        description: 表名称
        type: string
      tplCategory:
        type: string
      tree:
        type: boolean
      treeCode:
        type: string
      treeName:
        type: string
      treeParentCode:
        type: string
      updateBy:
        type: integer
      updatedAt:
        type: string
    type: object
info:
  contact: {}
  description: |-
    基于Gin + Vue + Element UI的前后端分离权限管理系统的接口文档
    添加qq群: 521386980 进入技术交流群 请先star，谢谢！
  license:
    name: MIT
    url: https://github.com/go-admin-team/go-admin/blob/master/LICENSE.md
  title: go-admin API
  version: 2.0.0
paths:
  /api/v1/app-config:
    get:
      description: 获取系统配置信息，主要注意这里不在验证权限
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties:
                    type: string
                  type: object
              type: object
      summary: 获取系统前台配置信息，主要注意这里不在验证权限
      tags:
      - 配置管理
  /api/v1/captcha:
    get:
      description: 获取验证码
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: string
                id:
                  type: string
                msg:
                  type: string
              type: object
      summary: 获取验证码
      tags:
      - 登陆
  /api/v1/db/columns/page:
    get:
      description: 数据库表列分页列表 / database table column page list
      parameters:
      - description: tableName / 数据表名称
        in: query
        name: tableName
        type: string
      - description: pageSize / 页条数
        in: query
        name: pageSize
        type: integer
      - description: pageIndex / 页码
        in: query
        name: pageIndex
        type: integer
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      summary: 分页列表数据 / page list data
      tags:
      - 工具 / 生成工具
  /api/v1/db/tables/page:
    get:
      description: 数据库表分页列表 / database table page list
      parameters:
      - description: tableName / 数据表名称
        in: query
        name: tableName
        type: string
      - description: pageSize / 页条数
        in: query
        name: pageSize
        type: integer
      - description: pageIndex / 页码
        in: query
        name: pageIndex
        type: integer
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      summary: 分页列表数据 / page list data
      tags:
      - 工具 / 生成工具
  /api/v1/dept:
    delete:
      description: 删除数据
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysDeptDeleteReq'
      responses:
        "200":
          description: '{"code": -1, "message": "删除失败"}'
          schema:
            type: string
      security:
      - Bearer: []
      summary: 删除部门
      tags:
      - 部门
    get:
      description: 分页列表
      parameters:
      - description: deptName
        in: query
        name: deptName
        type: string
      - description: deptId
        in: query
        name: deptId
        type: string
      - description: position
        in: query
        name: position
        type: string
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 分页部门列表数据
      tags:
      - 部门
    post:
      consumes:
      - application/json
      description: 获取JSON
      parameters:
      - description: data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysDeptInsertReq'
      responses:
        "200":
          description: '{"code": -1, "message": "添加失败"}'
          schema:
            type: string
      security:
      - Bearer: []
      summary: 添加部门
      tags:
      - 部门
  /api/v1/dept/{deptId}:
    get:
      description: 获取JSON
      parameters:
      - description: deptId
        in: path
        name: deptId
        type: string
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 获取部门数据
      tags:
      - 部门
    put:
      consumes:
      - application/json
      description: 获取JSON
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysDeptUpdateReq'
      responses:
        "200":
          description: '{"code": -1, "message": "添加失败"}'
          schema:
            type: string
      security:
      - Bearer: []
      summary: 修改部门
      tags:
      - 部门
  /api/v1/dict-data/option-select:
    get:
      description: 数据字典根据key获取
      parameters:
      - description: dictType
        in: query
        name: dictType
        required: true
        type: integer
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/dto.SysDictDataGetAllResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 数据字典根据key获取
      tags:
      - 字典数据
  /api/v1/dict/data:
    delete:
      description: 删除数据
      parameters:
      - description: body
        in: body
        name: dictCode
        required: true
        schema:
          $ref: '#/definitions/dto.SysDictDataDeleteReq'
      responses:
        "200":
          description: '{"code": 200, "message": "删除成功"}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 删除字典数据
      tags:
      - 字典数据
    get:
      description: 获取JSON
      parameters:
      - description: status
        in: query
        name: status
        type: string
      - description: dictCode
        in: query
        name: dictCode
        type: string
      - description: dictType
        in: query
        name: dictType
        type: string
      - description: 页条数
        in: query
        name: pageSize
        type: integer
      - description: 页码
        in: query
        name: pageIndex
        type: integer
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 字典数据列表
      tags:
      - 字典数据
    post:
      consumes:
      - application/json
      description: 获取JSON
      parameters:
      - description: data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysDictDataInsertReq'
      responses:
        "200":
          description: '{"code": 200, "message": "添加成功"}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 添加字典数据
      tags:
      - 字典数据
  /api/v1/dict/data/{dictCode}:
    get:
      description: 获取JSON
      parameters:
      - description: 字典编码
        in: path
        name: dictCode
        required: true
        type: integer
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 通过编码获取字典数据
      tags:
      - 字典数据
    put:
      consumes:
      - application/json
      description: 获取JSON
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysDictDataUpdateReq'
      responses:
        "200":
          description: '{"code": 200, "message": "修改成功"}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 修改字典数据
      tags:
      - 字典数据
  /api/v1/dict/type:
    delete:
      description: 删除数据
      parameters:
      - description: body
        in: body
        name: dictCode
        required: true
        schema:
          $ref: '#/definitions/dto.SysDictTypeDeleteReq'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 删除字典类型
      tags:
      - 字典类型
    get:
      description: 获取JSON
      parameters:
      - description: dictName
        in: query
        name: dictName
        type: string
      - description: dictId
        in: query
        name: dictId
        type: string
      - description: dictType
        in: query
        name: dictType
        type: string
      - description: 页条数
        in: query
        name: pageSize
        type: integer
      - description: 页码
        in: query
        name: pageIndex
        type: integer
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 字典类型列表数据
      tags:
      - 字典类型
    post:
      consumes:
      - application/json
      description: 获取JSON
      parameters:
      - description: data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysDictTypeInsertReq'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 添加字典类型
      tags:
      - 字典类型
  /api/v1/dict/type-option-select:
    get:
      description: 获取JSON
      parameters:
      - description: dictName
        in: query
        name: dictName
        type: string
      - description: dictId
        in: query
        name: dictId
        type: string
      - description: dictType
        in: query
        name: dictType
        type: string
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 字典类型全部数据 代码生成使用接口
      tags:
      - 字典类型
  /api/v1/dict/type/{dictId}:
    get:
      description: 获取JSON
      parameters:
      - description: 字典类型编码
        in: path
        name: dictId
        required: true
        type: integer
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 字典类型通过字典id获取
      tags:
      - 字典类型
    put:
      consumes:
      - application/json
      description: 获取JSON
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysDictTypeUpdateReq'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 修改字典类型
      tags:
      - 字典类型
  /api/v1/getinfo:
    get:
      description: 获取JSON
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 获取个人信息
      tags:
      - 个人中心
  /api/v1/login:
    post:
      consumes:
      - application/json
      description: |-
        获取token
        LoginHandler can be used by clients to get a jwt token.
        Payload needs to be json in the form of {"username": "USERNAME", "password": "PASSWORD"}.
        Reply will be of the form {"token": "TOKEN"}.
        dev mode：It should be noted that all fields cannot be empty, and a value of 0 can be passed in addition to the account password
        注意：开发模式：需要注意全部字段不能为空，账号密码外可以传入0值
      parameters:
      - description: account
        in: body
        name: account
        required: true
        schema:
          $ref: '#/definitions/handler.Login'
      responses:
        "200":
          description: '{"code": 200, "expire": "2019-08-07T12:45:48+08:00", "token":
            ".***********************************************************************.-zvzHvbg0A"
            }'
          schema:
            type: string
      summary: 登陆
      tags:
      - 登陆
  /api/v1/menu:
    delete:
      description: 删除数据
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysMenuDeleteReq'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 删除菜单
      tags:
      - 菜单
    get:
      description: 获取JSON
      parameters:
      - description: menuName
        in: query
        name: menuName
        type: string
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: Menu列表数据
      tags:
      - 菜单
    post:
      consumes:
      - application/json
      description: 获取JSON
      parameters:
      - description: data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysMenuInsertReq'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 创建菜单
      tags:
      - 菜单
  /api/v1/menu/{id}:
    get:
      description: 获取JSON
      parameters:
      - description: id
        in: path
        name: id
        type: string
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: Menu详情数据
      tags:
      - 菜单
    put:
      consumes:
      - application/json
      description: 获取JSON
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysMenuUpdateReq'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 修改菜单
      tags:
      - 菜单
  /api/v1/menuTreeselect/{roleId}:
    get:
      consumes:
      - application/json
      description: 获取JSON
      parameters:
      - description: roleId
        in: path
        name: roleId
        required: true
        type: integer
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 角色修改使用的菜单列表
      tags:
      - 菜单
  /api/v1/menurole:
    get:
      description: 获取JSON
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 根据登录角色名称获取菜单列表数据（左菜单使用）
      tags:
      - 菜单
  /api/v1/post:
    delete:
      description: 删除数据
      parameters:
      - description: 请求参数
        in: body
        name: id
        required: true
        schema:
          $ref: '#/definitions/dto.SysPostDeleteReq'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 删除岗位
      tags:
      - 岗位
    get:
      description: 获取JSON
      parameters:
      - description: postName
        in: query
        name: postName
        type: string
      - description: postCode
        in: query
        name: postCode
        type: string
      - description: postId
        in: query
        name: postId
        type: string
      - description: status
        in: query
        name: status
        type: string
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 岗位列表数据
      tags:
      - 岗位
    post:
      consumes:
      - application/json
      description: 获取JSON
      parameters:
      - description: data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysPostInsertReq'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 添加岗位
      tags:
      - 岗位
  /api/v1/post/{id}:
    put:
      consumes:
      - application/json
      description: 获取JSON
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysPostUpdateReq'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 修改岗位
      tags:
      - 岗位
  /api/v1/post/{postId}:
    get:
      description: 获取JSON
      parameters:
      - description: 编码
        in: path
        name: id
        required: true
        type: integer
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 获取岗位信息
      tags:
      - 岗位
  /api/v1/public/uploadFile:
    post:
      consumes:
      - multipart/form-data
      description: 获取JSON
      parameters:
      - description: type
        in: query
        name: type
        required: true
        type: string
      - description: file
        in: formData
        name: file
        required: true
        type: file
      responses:
        "200":
          description: '{"code": -1, "message": "添加失败"}'
          schema:
            type: string
      security:
      - Bearer: []
      summary: 上传图片
      tags:
      - 公共接口
  /api/v1/role:
    delete:
      description: 删除数据
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysRoleDeleteReq'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 删除用户角色
      tags:
      - 角色/Role
    get:
      description: Get JSON
      parameters:
      - description: roleName
        in: query
        name: roleName
        type: string
      - description: status
        in: query
        name: status
        type: string
      - description: roleKey
        in: query
        name: roleKey
        type: string
      - description: 页条数
        in: query
        name: pageSize
        type: integer
      - description: 页码
        in: query
        name: pageIndex
        type: integer
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 角色列表数据
      tags:
      - 角色/Role
    post:
      consumes:
      - application/json
      description: 获取JSON
      parameters:
      - description: data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysRoleInsertReq'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 创建角色
      tags:
      - 角色/Role
  /api/v1/role-status/{id}:
    put:
      consumes:
      - application/json
      description: 获取JSON
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.RoleDataScopeReq'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 更新角色数据权限
      tags:
      - 角色/Role
  /api/v1/role/{id}:
    get:
      description: 获取JSON
      parameters:
      - description: roleId
        in: path
        name: roleId
        type: string
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 获取Role数据
      tags:
      - 角色/Role
    put:
      consumes:
      - application/json
      description: 获取JSON
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysRoleUpdateReq'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 修改用户角色
      tags:
      - 角色/Role
  /api/v1/server-monitor:
    get:
      description: 获取JSON
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 系统信息
      tags:
      - 系统信息
  /api/v1/set-config:
    get:
      consumes:
      - application/json
      description: 界面操作设置配置值的获取
      responses:
        "200":
          description: '{"code": 200, "message": "修改成功"}'
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
              type: object
      security:
      - Bearer: []
      summary: 获取配置
      tags:
      - 配置管理
    put:
      consumes:
      - application/json
      description: 界面操作设置配置值
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          items:
            $ref: '#/definitions/dto.GetSetSysConfigReq'
          type: array
      responses:
        "200":
          description: '{"code": 200, "message": "修改成功"}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 设置配置
      tags:
      - 配置管理
  /api/v1/sys-api:
    delete:
      description: 删除接口管理
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysApiDeleteReq'
      responses:
        "200":
          description: '{"code": 200, "message": "删除成功"}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 删除接口管理
      tags:
      - 接口管理
    get:
      description: 获取接口管理列表
      parameters:
      - description: 名称
        in: query
        name: name
        type: string
      - description: 标题
        in: query
        name: title
        type: string
      - description: 地址
        in: query
        name: path
        type: string
      - description: 类型
        in: query
        name: action
        type: string
      - description: 页条数
        in: query
        name: pageSize
        type: integer
      - description: 页码
        in: query
        name: pageIndex
        type: integer
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/response.Page'
                  - properties:
                      list:
                        items:
                          $ref: '#/definitions/models.SysApi'
                        type: array
                    type: object
              type: object
      security:
      - Bearer: []
      summary: 获取接口管理列表
      tags:
      - 接口管理
  /api/v1/sys-api/{id}:
    get:
      description: 获取接口管理
      parameters:
      - description: id
        in: path
        name: id
        type: string
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SysApi'
              type: object
      security:
      - Bearer: []
      summary: 获取接口管理
      tags:
      - 接口管理
    put:
      consumes:
      - application/json
      description: 修改接口管理
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysApiUpdateReq'
      responses:
        "200":
          description: '{"code": 200, "message": "修改成功"}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 修改接口管理
      tags:
      - 接口管理
  /api/v1/sys-config:
    delete:
      description: 删除配置管理
      parameters:
      - description: ids
        in: body
        name: ids
        schema:
          items:
            type: integer
          type: array
      responses:
        "200":
          description: '{"code": 200, "message": "删除成功"}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 删除配置管理
      tags:
      - 配置管理
    get:
      description: 获取配置管理列表
      parameters:
      - description: 名称
        in: query
        name: configName
        type: string
      - description: key
        in: query
        name: configKey
        type: string
      - description: 类型
        in: query
        name: configType
        type: string
      - description: 是否前端
        in: query
        name: isFrontend
        type: integer
      - description: 页条数
        in: query
        name: pageSize
        type: integer
      - description: 页码
        in: query
        name: pageIndex
        type: integer
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/response.Page'
                  - properties:
                      list:
                        items:
                          $ref: '#/definitions/models.SysApi'
                        type: array
                    type: object
              type: object
      security:
      - Bearer: []
      summary: 获取配置管理列表
      tags:
      - 配置管理
    post:
      consumes:
      - application/json
      description: 创建配置管理
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysConfigControl'
      responses:
        "200":
          description: '{"code": 200, "message": "创建成功"}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 创建配置管理
      tags:
      - 配置管理
  /api/v1/sys-config/{id}:
    get:
      description: 根据Key获取SysConfig的Service
      parameters:
      - description: configKey
        in: path
        name: configKey
        type: string
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.SysConfigByKeyReq'
              type: object
      security:
      - Bearer: []
      summary: 根据Key获取SysConfig的Service
      tags:
      - 配置管理
    put:
      consumes:
      - application/json
      description: 修改配置管理
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysConfigControl'
      responses:
        "200":
          description: '{"code": 200, "message": "修改成功"}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 修改配置管理
      tags:
      - 配置管理
  /api/v1/sys-login-log:
    delete:
      description: 登录日志删除
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysLoginLogDeleteReq'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 登录日志删除
      tags:
      - 登录日志
    get:
      description: 获取JSON
      parameters:
      - description: 用户名
        in: query
        name: username
        type: string
      - description: ip地址
        in: query
        name: ipaddr
        type: string
      - description: 归属地
        in: query
        name: loginLocation
        type: string
      - description: 状态
        in: query
        name: status
        type: string
      - description: 开始时间
        in: query
        name: beginTime
        type: string
      - description: 结束时间
        in: query
        name: endTime
        type: string
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 登录日志列表
      tags:
      - 登录日志
  /api/v1/sys-login-log/{id}:
    get:
      description: 获取JSON
      parameters:
      - description: id
        in: path
        name: id
        type: string
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 登录日志通过id获取
      tags:
      - 登录日志
  /api/v1/sys-opera-log:
    delete:
      description: 删除数据
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysOperaLogDeleteReq'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 删除操作日志
      tags:
      - 操作日志
    get:
      description: 获取JSON
      parameters:
      - description: title
        in: query
        name: title
        type: string
      - description: method
        in: query
        name: method
        type: string
      - description: requestMethod
        in: query
        name: requestMethod
        type: string
      - description: operUrl
        in: query
        name: operUrl
        type: string
      - description: operIp
        in: query
        name: operIp
        type: string
      - description: status
        in: query
        name: status
        type: string
      - description: beginTime
        in: query
        name: beginTime
        type: string
      - description: endTime
        in: query
        name: endTime
        type: string
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 操作日志列表
      tags:
      - 操作日志
  /api/v1/sys-opera-log/{id}:
    get:
      description: 获取JSON
      parameters:
      - description: id
        in: path
        name: id
        type: string
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 操作日志通过id获取
      tags:
      - 操作日志
  /api/v1/sys-user:
    get:
      description: 获取JSON
      parameters:
      - description: username
        in: query
        name: username
        type: string
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            type: string
      security:
      - Bearer: []
      summary: 列表用户信息数据
      tags:
      - 用户
    post:
      consumes:
      - application/json
      description: 获取JSON
      parameters:
      - description: 用户数据
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysUserInsertReq'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 创建用户
      tags:
      - 用户
  /api/v1/sys-user/{userId}:
    delete:
      description: 删除数据
      parameters:
      - description: userId
        in: path
        name: userId
        required: true
        type: integer
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 删除用户数据
      tags:
      - 用户
    get:
      description: 获取JSON
      parameters:
      - description: 用户编码
        in: path
        name: userId
        required: true
        type: integer
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 获取用户
      tags:
      - 用户
    put:
      consumes:
      - application/json
      description: 获取JSON
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.SysUserUpdateReq'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 修改用户数据
      tags:
      - 用户
  /api/v1/sys/tables/info:
    post:
      consumes:
      - application/json
      description: 添加表结构
      parameters:
      - description: tableName / 数据表名称
        in: query
        name: tables
        type: string
      responses:
        "200":
          description: '{"code": -1, "message": "添加失败"}'
          schema:
            type: string
      security:
      - Bearer: []
      summary: 添加表结构
      tags:
      - 工具 / 生成工具
    put:
      consumes:
      - application/json
      description: 修改表结构
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/tools.SysTables'
      responses:
        "200":
          description: '{"code": -1, "message": "添加失败"}'
          schema:
            type: string
      security:
      - Bearer: []
      summary: 修改表结构
      tags:
      - 工具 / 生成工具
  /api/v1/sys/tables/info/{tableId}:
    delete:
      description: 删除表结构
      parameters:
      - description: tableId
        in: path
        name: tableId
        required: true
        type: integer
      responses:
        "200":
          description: '{"code": -1, "message": "删除失败"}'
          schema:
            type: string
      summary: 删除表结构
      tags:
      - 工具 / 生成工具
    get:
      description: 获取JSON
      parameters:
      - description: configKey
        in: path
        name: configKey
        required: true
        type: integer
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 获取配置
      tags:
      - 工具 / 生成工具
  /api/v1/sys/tables/page:
    get:
      description: 生成表分页列表
      parameters:
      - description: tableName / 数据表名称
        in: query
        name: tableName
        type: string
      - description: pageSize / 页条数
        in: query
        name: pageSize
        type: integer
      - description: pageIndex / 页码
        in: query
        name: pageIndex
        type: integer
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      summary: 分页列表数据
      tags:
      - 工具 / 生成工具
  /api/v1/user/avatar:
    post:
      consumes:
      - multipart/form-data
      description: 获取JSON
      parameters:
      - description: file
        in: formData
        name: file
        required: true
        type: file
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 修改头像
      tags:
      - 个人中心
  /api/v1/user/profile:
    get:
      description: 获取JSON
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 获取个人中心用户
      tags:
      - 个人中心
  /api/v1/user/pwd/reset:
    put:
      consumes:
      - application/json
      description: 获取JSON
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.ResetSysUserPwdReq'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 重置用户密码
      tags:
      - 用户
  /api/v1/user/pwd/set:
    put:
      consumes:
      - application/json
      description: 获取JSON
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.PassWord'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 修改密码
      tags:
      - 用户
  /api/v1/user/status:
    put:
      consumes:
      - application/json
      description: 获取JSON
      parameters:
      - description: body
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.UpdateSysUserStatusReq'
      responses:
        "200":
          description: '{"code": 200, "data": [...]}'
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: 修改用户状态
      tags:
      - 用户
  /logout:
    post:
      consumes:
      - application/json
      description: 获取token
      responses:
        "200":
          description: '{"code": 200, "msg": "成功退出系统" }'
          schema:
            type: string
      security:
      - Bearer: []
      summary: 退出登录
securityDefinitions:
  Bearer:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
