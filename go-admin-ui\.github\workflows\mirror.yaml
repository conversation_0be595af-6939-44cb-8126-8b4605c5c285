name: 'GitHub Actions Mirror'

on: [push, delete]

jobs:
  mirror_to_gitee:
    runs-on: ubuntu-latest
    steps:
      - name: 'Checkout'
        uses: actions/checkout@v1
      - name: 'Mirror to gitee'
        uses: pixta-dev/repository-mirroring-action@v1
        with:
          target_repo_url:
            *************:go-admin-team/go-admin-ui.git
          ssh_private_key:
            ${{ secrets.GITEE_KEY }}
  mirror_to_gitlab:
    runs-on: ubuntu-latest
    steps:
      - name: 'Checkout'
        uses: actions/checkout@v1
      - name: 'Mirror to gitlab'
        uses: pixta-dev/repository-mirroring-action@v1
        with:
          target_repo_url:
            **************:go-admin-team/go-admin-ui.git
          ssh_private_key:
            ${{ secrets.GITLAB_KEY }}
