<template>
  <div class="profile-container">
    <el-row :gutter="20">
      <el-col :span="6" :xs="24">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>个人信息</span>
            </div>
          </template>
          <div class="user-profile">
            <div class="user-avatar">
              <img :src="userInfo?.avatar || defaultAvatar" alt="avatar" />
            </div>
            <div class="user-info">
              <h3>{{ userInfo?.nickName || userInfo?.username }}</h3>
              <p>{{ userInfo?.email }}</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="18" :xs="24">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>基本资料</span>
            </div>
          </template>
          
          <el-form
            ref="profileFormRef"
            :model="profileForm"
            :rules="profileRules"
            label-width="80px"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="用户昵称" prop="nickName">
                  <el-input v-model="profileForm.nickName" maxlength="30" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="手机号码" prop="phone">
                  <el-input v-model="profileForm.phone" maxlength="11" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row>
              <el-col :span="12">
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="profileForm.email" maxlength="50" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="性别">
                  <el-radio-group v-model="profileForm.sex">
                    <el-radio value="0">男</el-radio>
                    <el-radio value="1">女</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item>
              <el-button type="primary" @click="submitProfile">保存</el-button>
              <el-button @click="resetProfile">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { validEmail, validPhone } from '@/utils/validate'

const userStore = useUserStore()

const profileFormRef = ref<FormInstance>()

const userInfo = computed(() => userStore.userInfo)
const defaultAvatar = 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'

const profileForm = reactive({
  nickName: '',
  phone: '',
  email: '',
  sex: '0',
})

const profileRules: FormRules = {
  nickName: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }],
  email: [
    { required: true, message: '邮箱地址不能为空', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!validEmail(value)) {
          callback(new Error('请输入正确的邮箱地址'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
  phone: [
    { required: true, message: '手机号码不能为空', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!validPhone(value)) {
          callback(new Error('请输入正确的手机号码'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
}

function initForm() {
  if (userInfo.value) {
    profileForm.nickName = userInfo.value.nickName || ''
    profileForm.phone = userInfo.value.phone || ''
    profileForm.email = userInfo.value.email || ''
    profileForm.sex = userInfo.value.sex || '0'
  }
}

async function submitProfile() {
  if (!profileFormRef.value) return
  
  try {
    const valid = await profileFormRef.value.validate()
    if (!valid) return
    
    // 这里应该调用更新用户信息的API
    ElMessage.success('保存成功')
  } catch (error) {
    console.error('Update profile error:', error)
  }
}

function resetProfile() {
  initForm()
}

onMounted(() => {
  initForm()
})
</script>

<style lang="scss" scoped>
.profile-container {
  padding: 20px;
  
  .user-profile {
    text-align: center;
    
    .user-avatar {
      margin-bottom: 20px;
      
      img {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
      }
    }
    
    .user-info {
      h3 {
        margin: 0 0 10px 0;
        color: #303133;
      }
      
      p {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }
}
</style>
