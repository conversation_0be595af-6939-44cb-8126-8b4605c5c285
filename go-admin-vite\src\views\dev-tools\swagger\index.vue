<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>Swagger 接口文档</span>
        </div>
      </template>
      
      <div class="swagger-container">
        <iframe 
          :src="swaggerUrl" 
          width="100%" 
          height="800px" 
          frameborder="0"
          @load="handleLoad"
        ></iframe>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const swaggerUrl = ref('http://localhost:8000/swagger/admin/index.html')

function handleLoad() {
  console.log('Swagger 文档加载完成')
}

onMounted(() => {
  console.log('Swagger 页面已挂载')
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.swagger-container {
  width: 100%;
  height: 800px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}
</style>
