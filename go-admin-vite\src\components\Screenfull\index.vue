<template>
  <div @click="click">
    <svg
      :class="{ 'is-active': isFullscreen }"
      class="screenfull-svg"
      fill="currentColor"
      height="20"
      width="20"
      viewBox="0 0 1024 1024"
    >
      <path
        v-if="!isFullscreen"
        d="M290.816 824.32l0-134.144-134.144 0-45.056 45.056 0 134.144 134.144 0L290.816 824.32zM200.704 333.824l134.144 0 0-134.144-45.056-45.056-134.144 0 0 134.144L200.704 333.824zM778.24 289.792l-134.144 0 0 134.144 45.056 45.056 134.144 0 0-134.144L778.24 289.792zM689.152 779.264l134.144 0 45.056-45.056 0-134.144-134.144 0 0 134.144L689.152 779.264z"
      />
      <path
        v-else
        d="M333.824 823.296l0-134.144 134.144 0 45.056 45.056 0 134.144-134.144 0L333.824 823.296zM423.936 333.824l-134.144 0 0-134.144 45.056-45.056 134.144 0 0 134.144L423.936 333.824zM645.12 289.792l134.144 0 0 134.144-45.056 45.056-134.144 0 0-134.144L645.12 289.792zM734.208 779.264l-134.144 0-45.056-45.056 0-134.144 134.144 0 0 134.144L734.208 779.264z"
      />
    </svg>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

const isFullscreen = ref(false)

function click() {
  if (!document.fullscreenEnabled) {
    ElMessage.warning('您的浏览器不支持全屏模式')
    return false
  }
  
  if (!isFullscreen.value) {
    requestFullscreen()
  } else {
    exitFullscreen()
  }
}

function requestFullscreen() {
  const element = document.documentElement
  if (element.requestFullscreen) {
    element.requestFullscreen()
  } else if ((element as any).webkitRequestFullscreen) {
    ;(element as any).webkitRequestFullscreen()
  } else if ((element as any).mozRequestFullScreen) {
    ;(element as any).mozRequestFullScreen()
  } else if ((element as any).msRequestFullscreen) {
    ;(element as any).msRequestFullscreen()
  }
}

function exitFullscreen() {
  if (document.exitFullscreen) {
    document.exitFullscreen()
  } else if ((document as any).webkitExitFullscreen) {
    ;(document as any).webkitExitFullscreen()
  } else if ((document as any).mozCancelFullScreen) {
    ;(document as any).mozCancelFullScreen()
  } else if ((document as any).msExitFullscreen) {
    ;(document as any).msExitFullscreen()
  }
}

function change() {
  isFullscreen.value = !!document.fullscreenElement
}

onMounted(() => {
  document.addEventListener('fullscreenchange', change)
  document.addEventListener('webkitfullscreenchange', change)
  document.addEventListener('mozfullscreenchange', change)
  document.addEventListener('msfullscreenchange', change)
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', change)
  document.removeEventListener('webkitfullscreenchange', change)
  document.removeEventListener('mozfullscreenchange', change)
  document.removeEventListener('msfullscreenchange', change)
})
</script>

<style scoped>
.screenfull-svg {
  cursor: pointer;
  fill: #5a5e66;
  width: 20px;
  height: 20px;
  vertical-align: 10px;
}
</style>
