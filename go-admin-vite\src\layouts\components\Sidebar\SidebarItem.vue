<template>
  <div v-if="!item.meta?.hidden">
    <template
      v-if="
        hasOneShowingChild(item.children, item) &&
        (!onlyOneChild.children || onlyOneChild.noShowingChildren) &&
        !item.meta?.alwaysShow
      "
    >
      <AppLink v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)">
        <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{ 'submenu-title-noDropdown': !isNest }">
          <SvgIcon v-if="onlyOneChild.meta.icon" :name="onlyOneChild.meta.icon" />
          <template #title>
            <span>{{ onlyOneChild.meta.title }}</span>
          </template>
        </el-menu-item>
      </AppLink>
    </template>

    <el-sub-menu v-else ref="subMenu" :index="resolvePath(item.path)" teleported>
      <template #title>
        <SvgIcon v-if="item.meta?.icon" :name="item.meta.icon" />
        <span>{{ item.meta?.title }}</span>
      </template>
      <SidebarItem
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-sub-menu>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { RouteRecordRaw } from 'vue-router'
import { isExternal } from '@/utils/validate'
import AppLink from './Link.vue'
import SvgIcon from '@/components/SvgIcon/index.vue'

interface Props {
  item: RouteRecordRaw
  isNest?: boolean
  basePath?: string
}

const props = withDefaults(defineProps<Props>(), {
  isNest: false,
  basePath: '',
})

const onlyOneChild = ref<RouteRecordRaw>({} as RouteRecordRaw)

function hasOneShowingChild(children: RouteRecordRaw[] = [], parent: RouteRecordRaw) {
  const showingChildren = children.filter((item) => {
    if (item.meta?.hidden) {
      return false
    } else {
      // 临时设置（如果只有一个显示子路由，将使用）
      onlyOneChild.value = item
      return true
    }
  })

  // 当只有一个子路由时，默认显示子路由
  if (showingChildren.length === 1) {
    return true
  }

  // 如果没有子路由显示，显示父路由
  if (showingChildren.length === 0) {
    onlyOneChild.value = { ...parent, path: '', noShowingChildren: true }
    return true
  }

  return false
}

function resolvePath(routePath: string) {
  if (isExternal(routePath)) {
    return routePath
  }
  if (isExternal(props.basePath || '')) {
    return props.basePath || ''
  }
  return joinPath(props.basePath || '', routePath)
}

// 简单的路径拼接函数
function joinPath(basePath: string, path: string): string {
  if (path.startsWith('/')) {
    return path
  }
  if (!basePath) {
    return path
  }
  const base = basePath.endsWith('/') ? basePath.slice(0, -1) : basePath
  const relativePath = path.startsWith('/') ? path : '/' + path
  return base + relativePath
}
</script>

<style lang="scss" scoped>
.nest-menu .el-sub-menu > .el-sub-menu__title,
.el-sub-menu .el-menu-item {
  min-width: 0 !important;
}
</style>
