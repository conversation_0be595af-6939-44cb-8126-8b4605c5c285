<template>
  <el-dropdown trigger="click" @command="handleSetSize">
    <div>
      <svg
        class="size-icon"
        viewBox="0 0 1024 1024"
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
      >
        <path
          d="M384 384h256c70.4 0 128 57.6 128 128s-57.6 128-128 128H384c-70.4 0-128-57.6-128-128s57.6-128 128-128z m0 192h256c35.2 0 64-28.8 64-64s-28.8-64-64-64H384c-35.2 0-64 28.8-64 64s28.8 64 64 64z"
        />
      </svg>
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item :disabled="size === 'large'" command="large">
          大号
        </el-dropdown-item>
        <el-dropdown-item :disabled="size === 'default'" command="default">
          默认
        </el-dropdown-item>
        <el-dropdown-item :disabled="size === 'small'" command="small">
          小号
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@/stores/app'
import { ElMessage } from 'element-plus'

const appStore = useAppStore()

const size = computed(() => appStore.size)

function handleSetSize(size: 'large' | 'default' | 'small') {
  appStore.setSize(size)
  ElMessage.success('切换成功')
  
  // 刷新页面以应用新的尺寸
  setTimeout(() => {
    window.location.reload()
  }, 500)
}
</script>

<style scoped>
.size-icon {
  cursor: pointer;
  fill: #5a5e66;
  width: 20px;
  height: 20px;
  vertical-align: 10px;
}
</style>
