name: Build CI

on: 
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: checkout
        uses: actions/checkout@v3
      
      - name: install
        run: yarn

      - name: install sass-migrator
        run: npm install -g sass-migrator

      - name: sass-migrator division **/*.scss 
        run: sass-migrator division **/*.scss 

      - name: Build
        run: npm run build:prod

      - name: ClearFile
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}  # 下面三个配置与上一步类似
          username: ${{ secrets.SSH_USERNAME }}
          key: ${{ secrets.DEPLOY_KEY }}
          # 重启的脚本，根据自身情况做相应改动，一般要做的是migrate数据库以及重启服务器
          script: |
            sudo rm -rf /www/vue2/*

      - name: Deploy to Server      # 第四步，rsync推送文件
        uses: AEnterprise/rsync-deploy@v1.0  # 使用别人包装好的步骤镜像
        env:
          DEPLOY_KEY: ${{ secrets.DEPLOY_KEY }}   # 引用配置，SSH私钥
          ARGS: -avz --delete --exclude='*.pyc'   # rsync参数，排除.pyc文件
          SERVER_PORT: '22'  # SSH端口
          FOLDER: ./dist/*  # 要推送的文件夹，路径相对于代码仓库的根目录
          SERVER_IP: ${{ secrets.SSH_HOST }}  # 引用配置，服务器的host名（IP或者域名domain.com）
          USERNAME: ${{ secrets.SSH_USERNAME }}  # 引用配置，服务器登录名
          SERVER_DESTINATION: /www/vue2/ # 部署到目标文件夹