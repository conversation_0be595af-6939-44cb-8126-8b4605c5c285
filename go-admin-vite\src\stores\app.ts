import { ref } from 'vue'
import { defineStore } from 'pinia'

export interface AppState {
  sidebar: {
    opened: boolean
    withoutAnimation: boolean
  }
  device: 'desktop' | 'mobile'
  size: 'large' | 'default' | 'small'
  theme: 'light' | 'dark'
  language: string
}

export const useAppStore = defineStore('app', () => {
  const sidebar = ref({
    opened: localStorage.getItem('sidebarStatus') !== 'closed',
    withoutAnimation: false,
  })
  
  const device = ref<'desktop' | 'mobile'>('desktop')
  const size = ref<'large' | 'default' | 'small'>(
    (localStorage.getItem('size') as 'large' | 'default' | 'small') || 'default'
  )
  const theme = ref<'light' | 'dark'>(
    (localStorage.getItem('theme') as 'light' | 'dark') || 'light'
  )
  const language = ref(localStorage.getItem('language') || 'zh-cn')

  // 切换侧边栏
  function toggleSidebar(): void {
    sidebar.value.opened = !sidebar.value.opened
    sidebar.value.withoutAnimation = false
    
    if (sidebar.value.opened) {
      localStorage.setItem('sidebarStatus', 'opened')
    } else {
      localStorage.setItem('sidebarStatus', 'closed')
    }
  }

  // 关闭侧边栏
  function closeSidebar(withoutAnimation = false): void {
    sidebar.value.opened = false
    sidebar.value.withoutAnimation = withoutAnimation
    localStorage.setItem('sidebarStatus', 'closed')
  }

  // 切换设备类型
  function toggleDevice(deviceType: 'desktop' | 'mobile'): void {
    device.value = deviceType
  }

  // 设置组件大小
  function setSize(sizeValue: 'large' | 'default' | 'small'): void {
    size.value = sizeValue
    localStorage.setItem('size', sizeValue)
  }

  // 切换主题
  function toggleTheme(): void {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
    localStorage.setItem('theme', theme.value)
    
    // 更新 HTML 类名
    const html = document.documentElement
    if (theme.value === 'dark') {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
  }

  // 设置主题
  function setTheme(themeValue: 'light' | 'dark'): void {
    theme.value = themeValue
    localStorage.setItem('theme', themeValue)
    
    // 更新 HTML 类名
    const html = document.documentElement
    if (themeValue === 'dark') {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
  }

  // 设置语言
  function setLanguage(lang: string): void {
    language.value = lang
    localStorage.setItem('language', lang)
  }

  // 初始化主题
  function initTheme(): void {
    const html = document.documentElement
    if (theme.value === 'dark') {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
  }

  return {
    sidebar,
    device,
    size,
    theme,
    language,
    toggleSidebar,
    closeSidebar,
    toggleDevice,
    setSize,
    toggleTheme,
    setTheme,
    setLanguage,
    initTheme,
  }
})
