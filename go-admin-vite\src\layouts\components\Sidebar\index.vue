<template>
  <div class="sidebar-wrapper">
    <!-- Logo -->
    <Logo v-if="showLogo" :collapse="isCollapse" />
    
    <!-- 菜单 -->
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        background-color="#304156"
        text-color="#bfcbd9"
        :unique-opened="false"
        active-text-color="#409eff"
        :collapse-transition="false"
        mode="vertical"
      >
        <SidebarItem
          v-for="route in routes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { usePermissionStore } from '@/stores/permission'
import Logo from './Logo.vue'
import SidebarItem from './SidebarItem.vue'
// 移除这个导入，我们将直接在样式中使用变量

const route = useRoute()
const appStore = useAppStore()
const permissionStore = usePermissionStore()

const routes = computed(() => {
  // 使用常量路由，过滤掉隐藏的路由
  return permissionStore.sidebarRoutes.filter(route => !route.meta?.hidden)
})
const showLogo = computed(() => true)
const isCollapse = computed(() => !appStore.sidebar.opened)

const activeMenu = computed(() => {
  const { meta, path } = route
  // 如果设置了 activeMenu，使用 activeMenu
  if (meta?.activeMenu) {
    return meta.activeMenu as string
  }
  return path
})
</script>

<style lang="scss" scoped>
.sidebar-wrapper {
  height: 100%;
  background-color: var(--el-menu-bg-color);
}

.scrollbar-wrapper {
  overflow-x: hidden !important;
}

.el-scrollbar__bar.is-vertical {
  right: 0px;
}

.el-scrollbar__bar.is-horizontal {
  display: none;
}
</style>
