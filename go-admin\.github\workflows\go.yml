name: build

on:
  push:
    branches: [ master, dev ]
  pull_request:
    branches: [ master ]
env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:

  build:
    name: Build
    runs-on: ubuntu-latest
    steps:

    - name: Set up Go 1.18
      uses: actions/setup-go@v3
      with:
        go-version: 1.18
      id: go

    - name: Check out code into the Go module directory
      uses: actions/checkout@v3

    - name: Get dependencies
      run: go mod tidy
    - name: Build
      run: make build

    - name: Log in to the Container registry
      uses: docker/login-action@v2
      if: startsWith(${{github.ref}}, 'refs/tags/')
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata (tags, labels) for Docker
      id: meta
      if: startsWith(${{github.ref}}, 'refs/tags/')
      uses: docker/metadata-action@v4
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        flavor: |
          latest=auto
        tags: |
          type=schedule
          type=ref,event=tag
          type=sha,prefix=,format=long,enable=true,priority=100

    - name: Build and push Docker image
      uses: docker/build-push-action@v3
      if: startsWith(${{github.ref}}, 'refs/tags/')
      with:
        context: .
        file: scripts/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
