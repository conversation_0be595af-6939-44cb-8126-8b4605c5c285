<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>构建工具</span>
        </div>
      </template>
      
      <div class="build-info">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card shadow="hover">
              <template #header>
                <span>前端构建信息</span>
              </template>
              <el-descriptions :column="1" border>
                <el-descriptions-item label="框架">Vue 3 + TypeScript</el-descriptions-item>
                <el-descriptions-item label="构建工具">Vite</el-descriptions-item>
                <el-descriptions-item label="UI框架">Element Plus</el-descriptions-item>
                <el-descriptions-item label="状态管理">Pinia</el-descriptions-item>
                <el-descriptions-item label="路由">Vue Router 4</el-descriptions-item>
                <el-descriptions-item label="HTTP客户端">Axios</el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
          
          <el-col :span="12">
            <el-card shadow="hover">
              <template #header>
                <span>后端构建信息</span>
              </template>
              <el-descriptions :column="1" border>
                <el-descriptions-item label="语言">Go</el-descriptions-item>
                <el-descriptions-item label="框架">Gin</el-descriptions-item>
                <el-descriptions-item label="数据库">MySQL</el-descriptions-item>
                <el-descriptions-item label="ORM">GORM</el-descriptions-item>
                <el-descriptions-item label="权限">Casbin</el-descriptions-item>
                <el-descriptions-item label="文档">Swagger</el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="24">
            <el-card shadow="hover">
              <template #header>
                <span>构建操作</span>
              </template>
              <el-space>
                <el-button type="primary" icon="Refresh" @click="handleBuild">重新构建</el-button>
                <el-button type="success" icon="Download" @click="handleDeploy">部署应用</el-button>
                <el-button type="info" icon="View" @click="handleLogs">查看日志</el-button>
                <el-button type="warning" icon="Setting" @click="handleConfig">构建配置</el-button>
              </el-space>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'

function handleBuild() {
  ElMessage.info('构建功能待开发')
}

function handleDeploy() {
  ElMessage.info('部署功能待开发')
}

function handleLogs() {
  ElMessage.info('日志查看功能待开发')
}

function handleConfig() {
  ElMessage.info('配置功能待开发')
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.build-info {
  .el-card {
    margin-bottom: 20px;
  }
}
</style>
