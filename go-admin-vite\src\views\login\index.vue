<template>
  <div class="login-container">
    <div class="login-background">
      <div class="login-panel">
        <div class="login-header">
          <div class="logo">
            <img src="/favicon.ico" alt="Logo" class="logo-img" />
            <h1 class="title">Go Admin</h1>
          </div>
          <p class="subtitle">现代化管理后台系统</p>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          autocomplete="on"
          label-position="left"
        >
          <el-form-item prop="username">
            <el-input
              ref="usernameRef"
              v-model="loginForm.username"
              placeholder="请输入用户名"
              name="username"
              type="text"
              tabindex="1"
              autocomplete="on"
              size="large"
              class="login-input"
            >
              <template #prefix>
                <el-icon class="input-icon"><User /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-tooltip v-model:visible="capsTooltip" content="大写锁定已开启" placement="right" manual>
            <el-form-item prop="password">
              <el-input
                :key="passwordType"
                ref="passwordRef"
                v-model="loginForm.password"
                :type="passwordType"
                placeholder="请输入密码"
                name="password"
                tabindex="2"
                autocomplete="on"
                size="large"
                class="login-input"
                @keyup="checkCapslock"
                @blur="capsTooltip = false"
                @keyup.enter="handleLogin"
              >
                <template #prefix>
                  <el-icon class="input-icon"><Lock /></el-icon>
                </template>
                <template #suffix>
                  <el-icon class="password-icon" @click="showPwd">
                    <View v-if="passwordType === 'password'" />
                    <Hide v-else />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-tooltip>

          <el-form-item prop="code" v-if="captchaEnabled">
            <div class="captcha-container">
              <el-input
                v-model="loginForm.code"
                auto-complete="off"
                placeholder="请输入验证码"
                size="large"
                class="login-input captcha-input"
                @keyup.enter="handleLogin"
              >
                <template #prefix>
                  <el-icon class="input-icon"><Key /></el-icon>
                </template>
              </el-input>
              <div class="captcha-image" @click="getCode" :class="{ loading: captchaLoading }">
                <img v-if="codeUrl && !captchaLoading" :src="codeUrl" alt="验证码" />
                <div v-else class="captcha-loading">
                  <el-icon><Loading /></el-icon>
                  <span>{{ captchaLoading ? '加载中...' : '点击获取' }}</span>
                </div>
                <div class="captcha-refresh" v-if="codeUrl && !captchaLoading">点击刷新</div>
              </div>
            </div>
          </el-form-item>

          <el-button
            :loading="loading"
            type="primary"
            size="large"
            class="login-button"
            @click.prevent="handleLogin"
          >
            <el-icon v-if="!loading" class="login-icon"><Right /></el-icon>
            {{ loading ? '登录中...' : '登 录' }}
          </el-button>

          <div class="login-tips">
            <div class="tip-item">
              <el-icon><InfoFilled /></el-icon>
              <span>默认用户名: admin</span>
            </div>
            <div class="tip-item">
              <el-icon><InfoFilled /></el-icon>
              <span>默认密码: 123456</span>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, nextTick, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { User, Lock, Key, View, Hide, Right, InfoFilled, Loading } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { getCaptcha } from '@/api/auth'
import type { LoginForm } from '@/types'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const loginFormRef = ref<FormInstance>()
const usernameRef = ref()
const passwordRef = ref()

const loginForm = reactive<LoginForm>({
  username: 'admin',
  password: '123456',
  code: '',
  uuid: '',
})

const loginRules: FormRules = {
  username: [
    { required: true, trigger: 'blur', message: '请输入用户名' },
    { min: 2, message: '用户名长度不能少于2位', trigger: 'blur' },
  ],
  password: [
    { required: true, trigger: 'blur', message: '请输入密码' },
    { min: 3, message: '密码长度不能少于3位', trigger: 'blur' },
  ],
  code: [
    { required: true, trigger: 'blur', message: '请输入验证码' },
    { len: 4, message: '验证码长度为4位', trigger: 'blur' },
  ],
}

const passwordType = ref('password')
const capsTooltip = ref(false)
const loading = ref(false)
const captchaEnabled = ref(true)
const codeUrl = ref('')
const captchaLoading = ref(false)

function showPwd() {
  if (passwordType.value === 'password') {
    passwordType.value = ''
  } else {
    passwordType.value = 'password'
  }
  nextTick(() => {
    passwordRef.value.focus()
  })
}

function checkCapslock(e: KeyboardEvent) {
  const { key } = e
  capsTooltip.value = !!(key && key.length === 1 && key >= 'A' && key <= 'Z')
}

async function getCode() {
  if (captchaLoading.value) return

  try {
    captchaLoading.value = true
    console.log('正在获取验证码...')
    const response = await getCaptcha()
    console.log('验证码响应:', response)

    // 后端返回格式: { code: 200, data: "base64", id: "uuid", msg: "success" }
    // 我们的拦截器对验证码API返回完整响应
    if (response && response.data && response.id) {
      codeUrl.value = response.data
      loginForm.uuid = response.id
      console.log('验证码设置成功, UUID:', loginForm.uuid)
    } else {
      throw new Error('验证码数据格式错误: ' + JSON.stringify(response))
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
    ElMessage.error('获取验证码失败，请重试')
  } finally {
    captchaLoading.value = false
  }
}

async function handleLogin() {
  if (!loginFormRef.value) return
  
  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    await userStore.login(loginForm)
    
    ElMessage.success('登录成功')
    
    const redirect = (route.query.redirect as string) || '/'
    router.push(redirect)
  } catch (error) {
    console.error('Login error:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getCode()
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
    opacity: 0.3;
  }

  .login-background {
    position: relative;
    z-index: 1;
  }

  .login-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 420px;
    max-width: 90vw;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .login-header {
    text-align: center;
    margin-bottom: 40px;

    .logo {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;

      .logo-img {
        width: 48px;
        height: 48px;
        margin-right: 12px;
      }

      .title {
        font-size: 28px;
        font-weight: 700;
        color: #2c3e50;
        margin: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    .subtitle {
      color: #7f8c8d;
      font-size: 14px;
      margin: 0;
      font-weight: 400;
    }
  }

  .login-form {
    .el-form-item {
      margin-bottom: 24px;
    }

    .login-input {
      :deep(.el-input__wrapper) {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 12px 16px;
        transition: all 0.3s ease;
        box-shadow: none;

        &:hover {
          border-color: #667eea;
        }

        &.is-focus {
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
      }

      .input-icon {
        color: #6c757d;
        font-size: 18px;
      }

      .password-icon {
        color: #6c757d;
        cursor: pointer;
        transition: color 0.3s ease;

        &:hover {
          color: #667eea;
        }
      }
    }

    .captcha-container {
      display: flex;
      gap: 12px;
      align-items: center;

      .captcha-input {
        flex: 1;
      }

      .captcha-image {
        width: 120px;
        height: 48px;
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;
        position: relative;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;

        &:hover {
          border-color: #667eea;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .captcha-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          background: #f8f9fa;
          color: #6c757d;
          gap: 4px;

          span {
            font-size: 12px;
          }
        }

        &.loading {
          pointer-events: none;
          opacity: 0.7;
        }

        .captcha-refresh {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: rgba(0, 0, 0, 0.7);
          color: white;
          font-size: 10px;
          text-align: center;
          padding: 2px;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover .captcha-refresh {
          opacity: 1;
        }
      }
    }

    .login-button {
      width: 100%;
      height: 48px;
      border-radius: 12px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 24px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
      }

      .login-icon {
        margin-right: 8px;
      }
    }

    .login-tips {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 16px;
      border-left: 4px solid #667eea;

      .tip-item {
        display: flex;
        align-items: center;
        color: #6c757d;
        font-size: 14px;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .el-icon {
          margin-right: 8px;
          color: #667eea;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .login-panel {
      margin: 20px;
      padding: 30px 20px;
      width: auto;
    }

    .login-header {
      margin-bottom: 30px;

      .logo {
        .logo-img {
          width: 40px;
          height: 40px;
        }

        .title {
          font-size: 24px;
        }
      }

      .subtitle {
        font-size: 13px;
      }
    }

    .login-form {
      .captcha-container {
        flex-direction: column;
        gap: 16px;

        .captcha-image {
          width: 100%;
          height: 60px;
        }
      }

      .login-tips {
        padding: 12px;

        .tip-item {
          font-size: 13px;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .login-panel {
      margin: 10px;
      padding: 20px 15px;
      border-radius: 16px;
    }

    .login-header {
      margin-bottom: 24px;

      .logo {
        .title {
          font-size: 22px;
        }
      }
    }

    .login-form {
      .login-input {
        :deep(.el-input__wrapper) {
          padding: 10px 14px;
        }
      }

      .login-button {
        height: 44px;
        font-size: 15px;
      }
    }
  }
}
</style>
