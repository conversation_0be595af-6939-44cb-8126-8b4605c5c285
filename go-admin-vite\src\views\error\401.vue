<template>
  <div class="error-page">
    <div class="error-code">
      <img
        src="https://gw.alipayobjects.com/zos/rmsportal/GIyMDJnuqmcqPLpHCSkj.svg"
        alt="401"
        class="error-img"
      />
    </div>
    <div class="error-desc">
      <h1>401</h1>
      <h2>抱歉，你无权访问该页面</h2>
      <div class="error-handle">
        <el-button type="primary" size="large" @click="back">
          返回首页
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

function back() {
  router.push('/')
}
</script>

<style lang="scss" scoped>
.error-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  
  .error-code {
    .error-img {
      width: 400px;
      height: 300px;
    }
  }
  
  .error-desc {
    margin-left: 40px;
    
    h1 {
      color: #434e59;
      font-size: 72px;
      font-weight: bold;
      line-height: 72px;
      margin-bottom: 24px;
    }
    
    h2 {
      color: rgba(0, 0, 0, 0.45);
      font-size: 20px;
      font-weight: normal;
      line-height: 28px;
      margin-bottom: 32px;
    }
  }
}

@media (max-width: 768px) {
  .error-page {
    flex-direction: column;
    
    .error-code {
      .error-img {
        width: 300px;
        height: 225px;
      }
    }
    
    .error-desc {
      margin-left: 0;
      margin-top: 20px;
      text-align: center;
      
      h1 {
        font-size: 48px;
        line-height: 48px;
      }
      
      h2 {
        font-size: 16px;
      }
    }
  }
}
</style>
