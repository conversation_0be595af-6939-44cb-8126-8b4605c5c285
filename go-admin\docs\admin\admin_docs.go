// Package admin GENERATED BY SWAG; DO NOT EDIT
// This file was generated by swaggo/swag
package admin

import "github.com/swaggo/swag"

const docTemplateadmin = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "license": {
            "name": "MIT",
            "url": "https://github.com/go-admin-team/go-admin/blob/master/LICENSE.md"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/app-config": {
            "get": {
                "description": "获取系统配置信息，主要注意这里不在验证权限",
                "tags": [
                    "配置管理"
                ],
                "summary": "获取系统前台配置信息，主要注意这里不在验证权限",
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": {
                                                "type": "string"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/captcha": {
            "get": {
                "description": "获取验证码",
                "tags": [
                    "登陆"
                ],
                "summary": "获取验证码",
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "string"
                                        },
                                        "id": {
                                            "type": "string"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/db/columns/page": {
            "get": {
                "description": "数据库表列分页列表 / database table column page list",
                "tags": [
                    "工具 / 生成工具"
                ],
                "summary": "分页列表数据 / page list data",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tableName / 数据表名称",
                        "name": "tableName",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "pageSize / 页条数",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "pageIndex / 页码",
                        "name": "pageIndex",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/db/tables/page": {
            "get": {
                "description": "数据库表分页列表 / database table page list",
                "tags": [
                    "工具 / 生成工具"
                ],
                "summary": "分页列表数据 / page list data",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tableName / 数据表名称",
                        "name": "tableName",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "pageSize / 页条数",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "pageIndex / 页码",
                        "name": "pageIndex",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/dept": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "分页列表",
                "tags": [
                    "部门"
                ],
                "summary": "分页部门列表数据",
                "parameters": [
                    {
                        "type": "string",
                        "description": "deptName",
                        "name": "deptName",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "deptId",
                        "name": "deptId",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "position",
                        "name": "position",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "部门"
                ],
                "summary": "添加部门",
                "parameters": [
                    {
                        "description": "data",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysDeptInsertReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": -1, \"message\": \"添加失败\"}",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "删除数据",
                "tags": [
                    "部门"
                ],
                "summary": "删除部门",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysDeptDeleteReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": -1, \"message\": \"删除失败\"}",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/api/v1/dept/{deptId}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "部门"
                ],
                "summary": "获取部门数据",
                "parameters": [
                    {
                        "type": "string",
                        "description": "deptId",
                        "name": "deptId",
                        "in": "path"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "部门"
                ],
                "summary": "修改部门",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysDeptUpdateReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": -1, \"message\": \"添加失败\"}",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/api/v1/dict-data/option-select": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "数据字典根据key获取",
                "tags": [
                    "字典数据"
                ],
                "summary": "数据字典根据key获取",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "dictType",
                        "name": "dictType",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/dto.SysDictDataGetAllResp"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/dict/data": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "字典数据"
                ],
                "summary": "字典数据列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "status",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "dictCode",
                        "name": "dictCode",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "dictType",
                        "name": "dictType",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页条数",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码",
                        "name": "pageIndex",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "字典数据"
                ],
                "summary": "添加字典数据",
                "parameters": [
                    {
                        "description": "data",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysDictDataInsertReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"message\": \"添加成功\"}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "删除数据",
                "tags": [
                    "字典数据"
                ],
                "summary": "删除字典数据",
                "parameters": [
                    {
                        "description": "body",
                        "name": "dictCode",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysDictDataDeleteReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"message\": \"删除成功\"}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/dict/data/{dictCode}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "字典数据"
                ],
                "summary": "通过编码获取字典数据",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "字典编码",
                        "name": "dictCode",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "字典数据"
                ],
                "summary": "修改字典数据",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysDictDataUpdateReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"message\": \"修改成功\"}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/dict/type": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "字典类型"
                ],
                "summary": "字典类型列表数据",
                "parameters": [
                    {
                        "type": "string",
                        "description": "dictName",
                        "name": "dictName",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "dictId",
                        "name": "dictId",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "dictType",
                        "name": "dictType",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页条数",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码",
                        "name": "pageIndex",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "字典类型"
                ],
                "summary": "添加字典类型",
                "parameters": [
                    {
                        "description": "data",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysDictTypeInsertReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "删除数据",
                "tags": [
                    "字典类型"
                ],
                "summary": "删除字典类型",
                "parameters": [
                    {
                        "description": "body",
                        "name": "dictCode",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysDictTypeDeleteReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/dict/type-option-select": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "字典类型"
                ],
                "summary": "字典类型全部数据 代码生成使用接口",
                "parameters": [
                    {
                        "type": "string",
                        "description": "dictName",
                        "name": "dictName",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "dictId",
                        "name": "dictId",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "dictType",
                        "name": "dictType",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/dict/type/{dictId}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "字典类型"
                ],
                "summary": "字典类型通过字典id获取",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "字典类型编码",
                        "name": "dictId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "字典类型"
                ],
                "summary": "修改字典类型",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysDictTypeUpdateReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/getinfo": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "个人中心"
                ],
                "summary": "获取个人信息",
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/login": {
            "post": {
                "description": "获取token\nLoginHandler can be used by clients to get a jwt token.\nPayload needs to be json in the form of {\"username\": \"USERNAME\", \"password\": \"PASSWORD\"}.\nReply will be of the form {\"token\": \"TOKEN\"}.\ndev mode：It should be noted that all fields cannot be empty, and a value of 0 can be passed in addition to the account password\n注意：开发模式：需要注意全部字段不能为空，账号密码外可以传入0值",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "登陆"
                ],
                "summary": "登陆",
                "parameters": [
                    {
                        "description": "account",
                        "name": "account",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handler.Login"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"expire\": \"2019-08-07T12:45:48+08:00\", \"token\": \".***********************************************************************.-zvzHvbg0A\" }",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/api/v1/menu": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "菜单"
                ],
                "summary": "Menu列表数据",
                "parameters": [
                    {
                        "type": "string",
                        "description": "menuName",
                        "name": "menuName",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "菜单"
                ],
                "summary": "创建菜单",
                "parameters": [
                    {
                        "description": "data",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysMenuInsertReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "删除数据",
                "tags": [
                    "菜单"
                ],
                "summary": "删除菜单",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysMenuDeleteReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/menu/{id}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "菜单"
                ],
                "summary": "Menu详情数据",
                "parameters": [
                    {
                        "type": "string",
                        "description": "id",
                        "name": "id",
                        "in": "path"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "菜单"
                ],
                "summary": "修改菜单",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysMenuUpdateReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/menuTreeselect/{roleId}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "菜单"
                ],
                "summary": "角色修改使用的菜单列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "roleId",
                        "name": "roleId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/menurole": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "菜单"
                ],
                "summary": "根据登录角色名称获取菜单列表数据（左菜单使用）",
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/post": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "岗位"
                ],
                "summary": "岗位列表数据",
                "parameters": [
                    {
                        "type": "string",
                        "description": "postName",
                        "name": "postName",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "postCode",
                        "name": "postCode",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "postId",
                        "name": "postId",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "status",
                        "name": "status",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "岗位"
                ],
                "summary": "添加岗位",
                "parameters": [
                    {
                        "description": "data",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysPostInsertReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "删除数据",
                "tags": [
                    "岗位"
                ],
                "summary": "删除岗位",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "id",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysPostDeleteReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/post/{id}": {
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "岗位"
                ],
                "summary": "修改岗位",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysPostUpdateReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/post/{postId}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "岗位"
                ],
                "summary": "获取岗位信息",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "编码",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/public/uploadFile": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "multipart/form-data"
                ],
                "tags": [
                    "公共接口"
                ],
                "summary": "上传图片",
                "parameters": [
                    {
                        "type": "string",
                        "description": "type",
                        "name": "type",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "file",
                        "description": "file",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": -1, \"message\": \"添加失败\"}",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/api/v1/role": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Get JSON",
                "tags": [
                    "角色/Role"
                ],
                "summary": "角色列表数据",
                "parameters": [
                    {
                        "type": "string",
                        "description": "roleName",
                        "name": "roleName",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "status",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "roleKey",
                        "name": "roleKey",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页条数",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码",
                        "name": "pageIndex",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "角色/Role"
                ],
                "summary": "创建角色",
                "parameters": [
                    {
                        "description": "data",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysRoleInsertReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "删除数据",
                "tags": [
                    "角色/Role"
                ],
                "summary": "删除用户角色",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysRoleDeleteReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/role-status/{id}": {
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "角色/Role"
                ],
                "summary": "更新角色数据权限",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.RoleDataScopeReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/role/{id}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "角色/Role"
                ],
                "summary": "获取Role数据",
                "parameters": [
                    {
                        "type": "string",
                        "description": "roleId",
                        "name": "roleId",
                        "in": "path"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "角色/Role"
                ],
                "summary": "修改用户角色",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysRoleUpdateReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/server-monitor": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "系统信息"
                ],
                "summary": "系统信息",
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/set-config": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "界面操作设置配置值的获取",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "配置管理"
                ],
                "summary": "获取配置",
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"message\": \"修改成功\"}",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "界面操作设置配置值",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "配置管理"
                ],
                "summary": "设置配置",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/dto.GetSetSysConfigReq"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"message\": \"修改成功\"}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/sys-api": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取接口管理列表",
                "tags": [
                    "接口管理"
                ],
                "summary": "获取接口管理列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "名称",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "标题",
                        "name": "title",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "地址",
                        "name": "path",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "类型",
                        "name": "action",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页条数",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码",
                        "name": "pageIndex",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "allOf": [
                                                {
                                                    "$ref": "#/definitions/response.Page"
                                                },
                                                {
                                                    "type": "object",
                                                    "properties": {
                                                        "list": {
                                                            "type": "array",
                                                            "items": {
                                                                "$ref": "#/definitions/models.SysApi"
                                                            }
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "删除接口管理",
                "tags": [
                    "接口管理"
                ],
                "summary": "删除接口管理",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysApiDeleteReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"message\": \"删除成功\"}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/sys-api/{id}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取接口管理",
                "tags": [
                    "接口管理"
                ],
                "summary": "获取接口管理",
                "parameters": [
                    {
                        "type": "string",
                        "description": "id",
                        "name": "id",
                        "in": "path"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.SysApi"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "修改接口管理",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "接口管理"
                ],
                "summary": "修改接口管理",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysApiUpdateReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"message\": \"修改成功\"}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/sys-config": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取配置管理列表",
                "tags": [
                    "配置管理"
                ],
                "summary": "获取配置管理列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "名称",
                        "name": "configName",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "key",
                        "name": "configKey",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "类型",
                        "name": "configType",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "是否前端",
                        "name": "isFrontend",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页条数",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码",
                        "name": "pageIndex",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "allOf": [
                                                {
                                                    "$ref": "#/definitions/response.Page"
                                                },
                                                {
                                                    "type": "object",
                                                    "properties": {
                                                        "list": {
                                                            "type": "array",
                                                            "items": {
                                                                "$ref": "#/definitions/models.SysApi"
                                                            }
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "创建配置管理",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "配置管理"
                ],
                "summary": "创建配置管理",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysConfigControl"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"message\": \"创建成功\"}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "删除配置管理",
                "tags": [
                    "配置管理"
                ],
                "summary": "删除配置管理",
                "parameters": [
                    {
                        "description": "ids",
                        "name": "ids",
                        "in": "body",
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "integer"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"message\": \"删除成功\"}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/sys-config/{id}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "根据Key获取SysConfig的Service",
                "tags": [
                    "配置管理"
                ],
                "summary": "根据Key获取SysConfig的Service",
                "parameters": [
                    {
                        "type": "string",
                        "description": "configKey",
                        "name": "configKey",
                        "in": "path"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.SysConfigByKeyReq"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "修改配置管理",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "配置管理"
                ],
                "summary": "修改配置管理",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysConfigControl"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"message\": \"修改成功\"}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/sys-login-log": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "登录日志"
                ],
                "summary": "登录日志列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "username",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "ip地址",
                        "name": "ipaddr",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "归属地",
                        "name": "loginLocation",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "状态",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "开始时间",
                        "name": "beginTime",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "结束时间",
                        "name": "endTime",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "登录日志删除",
                "tags": [
                    "登录日志"
                ],
                "summary": "登录日志删除",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysLoginLogDeleteReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/sys-login-log/{id}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "登录日志"
                ],
                "summary": "登录日志通过id获取",
                "parameters": [
                    {
                        "type": "string",
                        "description": "id",
                        "name": "id",
                        "in": "path"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/sys-opera-log": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "操作日志"
                ],
                "summary": "操作日志列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "title",
                        "name": "title",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "method",
                        "name": "method",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "requestMethod",
                        "name": "requestMethod",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "operUrl",
                        "name": "operUrl",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "operIp",
                        "name": "operIp",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "status",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "beginTime",
                        "name": "beginTime",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "endTime",
                        "name": "endTime",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "删除数据",
                "tags": [
                    "操作日志"
                ],
                "summary": "删除操作日志",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysOperaLogDeleteReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/sys-opera-log/{id}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "操作日志"
                ],
                "summary": "操作日志通过id获取",
                "parameters": [
                    {
                        "type": "string",
                        "description": "id",
                        "name": "id",
                        "in": "path"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/sys-user": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "用户"
                ],
                "summary": "列表用户信息数据",
                "parameters": [
                    {
                        "type": "string",
                        "description": "username",
                        "name": "username",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "用户"
                ],
                "summary": "创建用户",
                "parameters": [
                    {
                        "description": "用户数据",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysUserInsertReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/sys-user/{userId}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "用户"
                ],
                "summary": "获取用户",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户编码",
                        "name": "userId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "用户"
                ],
                "summary": "修改用户数据",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SysUserUpdateReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "删除数据",
                "tags": [
                    "用户"
                ],
                "summary": "删除用户数据",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "userId",
                        "name": "userId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/sys/tables/info": {
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "修改表结构",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "工具 / 生成工具"
                ],
                "summary": "修改表结构",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/tools.SysTables"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": -1, \"message\": \"添加失败\"}",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "添加表结构",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "工具 / 生成工具"
                ],
                "summary": "添加表结构",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tableName / 数据表名称",
                        "name": "tables",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": -1, \"message\": \"添加失败\"}",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/api/v1/sys/tables/info/{tableId}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "工具 / 生成工具"
                ],
                "summary": "获取配置",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "configKey",
                        "name": "configKey",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "delete": {
                "description": "删除表结构",
                "tags": [
                    "工具 / 生成工具"
                ],
                "summary": "删除表结构",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "tableId",
                        "name": "tableId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": -1, \"message\": \"删除失败\"}",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/api/v1/sys/tables/page": {
            "get": {
                "description": "生成表分页列表",
                "tags": [
                    "工具 / 生成工具"
                ],
                "summary": "分页列表数据",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tableName / 数据表名称",
                        "name": "tableName",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "pageSize / 页条数",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "pageIndex / 页码",
                        "name": "pageIndex",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/user/avatar": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "multipart/form-data"
                ],
                "tags": [
                    "个人中心"
                ],
                "summary": "修改头像",
                "parameters": [
                    {
                        "type": "file",
                        "description": "file",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/user/profile": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "tags": [
                    "个人中心"
                ],
                "summary": "获取个人中心用户",
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/user/pwd/reset": {
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "用户"
                ],
                "summary": "重置用户密码",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.ResetSysUserPwdReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/user/pwd/set": {
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "用户"
                ],
                "summary": "修改密码",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.PassWord"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/user/status": {
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取JSON",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "用户"
                ],
                "summary": "修改用户状态",
                "parameters": [
                    {
                        "description": "body",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.UpdateSysUserStatusReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"data\": [...]}",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/logout": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "获取token",
                "consumes": [
                    "application/json"
                ],
                "summary": "退出登录",
                "responses": {
                    "200": {
                        "description": "{\"code\": 200, \"msg\": \"成功退出系统\" }",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "dto.GetSetSysConfigReq": {
            "type": "object",
            "properties": {
                "configKey": {
                    "type": "string"
                },
                "configValue": {
                    "type": "string"
                }
            }
        },
        "dto.PassWord": {
            "type": "object",
            "properties": {
                "newPassword": {
                    "type": "string"
                },
                "oldPassword": {
                    "type": "string"
                }
            }
        },
        "dto.ResetSysUserPwdReq": {
            "type": "object",
            "properties": {
                "createBy": {
                    "type": "integer"
                },
                "password": {
                    "type": "string"
                },
                "updateBy": {
                    "type": "integer"
                },
                "userId": {
                    "description": "用户ID",
                    "type": "integer"
                }
            }
        },
        "dto.RoleDataScopeReq": {
            "type": "object",
            "required": [
                "dataScope",
                "roleId"
            ],
            "properties": {
                "dataScope": {
                    "type": "string"
                },
                "deptIds": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "roleId": {
                    "type": "integer"
                }
            }
        },
        "dto.SysApiDeleteReq": {
            "type": "object",
            "properties": {
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "dto.SysApiUpdateReq": {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string"
                },
                "createBy": {
                    "type": "integer"
                },
                "handle": {
                    "type": "string"
                },
                "id": {
                    "description": "编码",
                    "type": "integer"
                },
                "path": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "updateBy": {
                    "type": "integer"
                }
            }
        },
        "dto.SysConfigByKeyReq": {
            "type": "object",
            "properties": {
                "configKey": {
                    "type": "string"
                }
            }
        },
        "dto.SysConfigControl": {
            "type": "object",
            "properties": {
                "configKey": {
                    "type": "string"
                },
                "configName": {
                    "type": "string"
                },
                "configType": {
                    "type": "string"
                },
                "configValue": {
                    "type": "string"
                },
                "createBy": {
                    "type": "integer"
                },
                "id": {
                    "description": "编码",
                    "type": "integer"
                },
                "isFrontend": {
                    "type": "string"
                },
                "remark": {
                    "type": "string"
                },
                "updateBy": {
                    "type": "integer"
                }
            }
        },
        "dto.SysDeptDeleteReq": {
            "type": "object",
            "properties": {
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "dto.SysDeptInsertReq": {
            "type": "object",
            "properties": {
                "createBy": {
                    "type": "integer"
                },
                "deptId": {
                    "description": "编码",
                    "type": "integer"
                },
                "deptName": {
                    "description": "部门名称",
                    "type": "string"
                },
                "deptPath": {
                    "description": "路径",
                    "type": "string"
                },
                "email": {
                    "description": "邮箱",
                    "type": "string"
                },
                "leader": {
                    "description": "负责人",
                    "type": "string"
                },
                "parentId": {
                    "description": "上级部门",
                    "type": "integer"
                },
                "phone": {
                    "description": "手机",
                    "type": "string"
                },
                "sort": {
                    "description": "排序",
                    "type": "integer"
                },
                "status": {
                    "description": "状态",
                    "type": "integer"
                },
                "updateBy": {
                    "type": "integer"
                }
            }
        },
        "dto.SysDeptUpdateReq": {
            "type": "object",
            "properties": {
                "createBy": {
                    "type": "integer"
                },
                "deptId": {
                    "description": "编码",
                    "type": "integer"
                },
                "deptName": {
                    "description": "部门名称",
                    "type": "string"
                },
                "deptPath": {
                    "description": "路径",
                    "type": "string"
                },
                "email": {
                    "description": "邮箱",
                    "type": "string"
                },
                "leader": {
                    "description": "负责人",
                    "type": "string"
                },
                "parentId": {
                    "description": "上级部门",
                    "type": "integer"
                },
                "phone": {
                    "description": "手机",
                    "type": "string"
                },
                "sort": {
                    "description": "排序",
                    "type": "integer"
                },
                "status": {
                    "description": "状态",
                    "type": "integer"
                },
                "updateBy": {
                    "type": "integer"
                }
            }
        },
        "dto.SysDictDataDeleteReq": {
            "type": "object",
            "properties": {
                "createBy": {
                    "type": "integer"
                },
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "updateBy": {
                    "type": "integer"
                }
            }
        },
        "dto.SysDictDataGetAllResp": {
            "type": "object",
            "properties": {
                "label": {
                    "type": "string"
                },
                "value": {
                    "type": "string"
                }
            }
        },
        "dto.SysDictDataInsertReq": {
            "type": "object",
            "properties": {
                "createBy": {
                    "type": "integer"
                },
                "cssClass": {
                    "type": "string"
                },
                "default": {
                    "type": "string"
                },
                "dictLabel": {
                    "type": "string"
                },
                "dictSort": {
                    "type": "integer"
                },
                "dictType": {
                    "type": "string"
                },
                "dictValue": {
                    "type": "string"
                },
                "isDefault": {
                    "type": "string"
                },
                "listClass": {
                    "type": "string"
                },
                "remark": {
                    "type": "string"
                },
                "status": {
                    "type": "integer"
                },
                "updateBy": {
                    "type": "integer"
                }
            }
        },
        "dto.SysDictDataUpdateReq": {
            "type": "object",
            "properties": {
                "createBy": {
                    "type": "integer"
                },
                "cssClass": {
                    "type": "string"
                },
                "default": {
                    "type": "string"
                },
                "dictLabel": {
                    "type": "string"
                },
                "dictSort": {
                    "type": "integer"
                },
                "dictType": {
                    "type": "string"
                },
                "dictValue": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "isDefault": {
                    "type": "string"
                },
                "listClass": {
                    "type": "string"
                },
                "remark": {
                    "type": "string"
                },
                "status": {
                    "type": "integer"
                },
                "updateBy": {
                    "type": "integer"
                }
            }
        },
        "dto.SysDictTypeDeleteReq": {
            "type": "object",
            "properties": {
                "createBy": {
                    "type": "integer"
                },
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "updateBy": {
                    "type": "integer"
                }
            }
        },
        "dto.SysDictTypeInsertReq": {
            "type": "object",
            "properties": {
                "createBy": {
                    "type": "integer"
                },
                "dictName": {
                    "type": "string"
                },
                "dictType": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "remark": {
                    "type": "string"
                },
                "status": {
                    "type": "integer"
                },
                "updateBy": {
                    "type": "integer"
                }
            }
        },
        "dto.SysDictTypeUpdateReq": {
            "type": "object",
            "properties": {
                "createBy": {
                    "type": "integer"
                },
                "dictName": {
                    "type": "string"
                },
                "dictType": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "remark": {
                    "type": "string"
                },
                "status": {
                    "type": "integer"
                },
                "updateBy": {
                    "type": "integer"
                }
            }
        },
        "dto.SysLoginLogDeleteReq": {
            "type": "object",
            "properties": {
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "dto.SysMenuDeleteReq": {
            "type": "object",
            "properties": {
                "createBy": {
                    "type": "integer"
                },
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "updateBy": {
                    "type": "integer"
                }
            }
        },
        "dto.SysMenuInsertReq": {
            "type": "object",
            "properties": {
                "action": {
                    "description": "请求方式",
                    "type": "string"
                },
                "apis": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "breadcrumb": {
                    "description": "是否面包屑",
                    "type": "string"
                },
                "component": {
                    "description": "组件",
                    "type": "string"
                },
                "createBy": {
                    "type": "integer"
                },
                "icon": {
                    "description": "图标",
                    "type": "string"
                },
                "isFrame": {
                    "description": "是否frame",
                    "type": "string"
                },
                "menuId": {
                    "description": "编码",
                    "type": "integer"
                },
                "menuName": {
                    "description": "菜单name",
                    "type": "string"
                },
                "menuType": {
                    "description": "菜单类型",
                    "type": "string"
                },
                "noCache": {
                    "description": "是否缓存",
                    "type": "boolean"
                },
                "parentId": {
                    "description": "上级菜单",
                    "type": "integer"
                },
                "path": {
                    "description": "路径",
                    "type": "string"
                },
                "paths": {
                    "description": "id路径",
                    "type": "string"
                },
                "permission": {
                    "description": "权限编码",
                    "type": "string"
                },
                "sort": {
                    "description": "排序",
                    "type": "integer"
                },
                "sysApi": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.SysApi"
                    }
                },
                "title": {
                    "description": "显示名称",
                    "type": "string"
                },
                "updateBy": {
                    "type": "integer"
                },
                "visible": {
                    "description": "是否显示",
                    "type": "string"
                }
            }
        },
        "dto.SysMenuUpdateReq": {
            "type": "object",
            "properties": {
                "action": {
                    "description": "请求方式",
                    "type": "string"
                },
                "apis": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "breadcrumb": {
                    "description": "是否面包屑",
                    "type": "string"
                },
                "component": {
                    "description": "组件",
                    "type": "string"
                },
                "createBy": {
                    "type": "integer"
                },
                "icon": {
                    "description": "图标",
                    "type": "string"
                },
                "isFrame": {
                    "description": "是否frame",
                    "type": "string"
                },
                "menuId": {
                    "description": "编码",
                    "type": "integer"
                },
                "menuName": {
                    "description": "菜单name",
                    "type": "string"
                },
                "menuType": {
                    "description": "菜单类型",
                    "type": "string"
                },
                "noCache": {
                    "description": "是否缓存",
                    "type": "boolean"
                },
                "parentId": {
                    "description": "上级菜单",
                    "type": "integer"
                },
                "path": {
                    "description": "路径",
                    "type": "string"
                },
                "paths": {
                    "description": "id路径",
                    "type": "string"
                },
                "permission": {
                    "description": "权限编码",
                    "type": "string"
                },
                "sort": {
                    "description": "排序",
                    "type": "integer"
                },
                "sysApi": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.SysApi"
                    }
                },
                "title": {
                    "description": "显示名称",
                    "type": "string"
                },
                "updateBy": {
                    "type": "integer"
                },
                "visible": {
                    "description": "是否显示",
                    "type": "string"
                }
            }
        },
        "dto.SysOperaLogDeleteReq": {
            "type": "object",
            "properties": {
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "dto.SysPostDeleteReq": {
            "type": "object",
            "properties": {
                "createBy": {
                    "type": "integer"
                },
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "updateBy": {
                    "type": "integer"
                }
            }
        },
        "dto.SysPostInsertReq": {
            "type": "object",
            "properties": {
                "createBy": {
                    "type": "integer"
                },
                "postCode": {
                    "type": "string"
                },
                "postId": {
                    "type": "integer"
                },
                "postName": {
                    "type": "string"
                },
                "remark": {
                    "type": "string"
                },
                "sort": {
                    "type": "integer"
                },
                "status": {
                    "type": "integer"
                },
                "updateBy": {
                    "type": "integer"
                }
            }
        },
        "dto.SysPostUpdateReq": {
            "type": "object",
            "properties": {
                "createBy": {
                    "type": "integer"
                },
                "postCode": {
                    "type": "string"
                },
                "postId": {
                    "type": "integer"
                },
                "postName": {
                    "type": "string"
                },
                "remark": {
                    "type": "string"
                },
                "sort": {
                    "type": "integer"
                },
                "status": {
                    "type": "integer"
                },
                "updateBy": {
                    "type": "integer"
                }
            }
        },
        "dto.SysRoleDeleteReq": {
            "type": "object",
            "properties": {
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "dto.SysRoleInsertReq": {
            "type": "object",
            "properties": {
                "admin": {
                    "type": "boolean"
                },
                "createBy": {
                    "type": "integer"
                },
                "dataScope": {
                    "type": "string"
                },
                "deptIds": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "flag": {
                    "description": "标记",
                    "type": "string"
                },
                "menuIds": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "remark": {
                    "description": "备注",
                    "type": "string"
                },
                "roleId": {
                    "description": "角色编码",
                    "type": "integer"
                },
                "roleKey": {
                    "description": "角色代码",
                    "type": "string"
                },
                "roleName": {
                    "description": "角色名称",
                    "type": "string"
                },
                "roleSort": {
                    "description": "角色排序",
                    "type": "integer"
                },
                "status": {
                    "description": "状态 1禁用 2正常",
                    "type": "string"
                },
                "sysDept": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.SysDept"
                    }
                },
                "sysMenu": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.SysMenu"
                    }
                },
                "updateBy": {
                    "type": "integer"
                }
            }
        },
        "dto.SysRoleUpdateReq": {
            "type": "object",
            "properties": {
                "admin": {
                    "type": "boolean"
                },
                "createBy": {
                    "type": "integer"
                },
                "dataScope": {
                    "type": "string"
                },
                "deptIds": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "flag": {
                    "description": "标记",
                    "type": "string"
                },
                "menuIds": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "remark": {
                    "description": "备注",
                    "type": "string"
                },
                "roleId": {
                    "description": "角色编码",
                    "type": "integer"
                },
                "roleKey": {
                    "description": "角色代码",
                    "type": "string"
                },
                "roleName": {
                    "description": "角色名称",
                    "type": "string"
                },
                "roleSort": {
                    "description": "角色排序",
                    "type": "integer"
                },
                "status": {
                    "description": "状态",
                    "type": "string"
                },
                "sysDept": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.SysDept"
                    }
                },
                "sysMenu": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.SysMenu"
                    }
                },
                "updateBy": {
                    "type": "integer"
                }
            }
        },
        "dto.SysUserInsertReq": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "createBy": {
                    "type": "integer"
                },
                "deptId": {
                    "type": "integer"
                },
                "email": {
                    "type": "string"
                },
                "nickName": {
                    "type": "string"
                },
                "password": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "postId": {
                    "type": "integer"
                },
                "remark": {
                    "type": "string"
                },
                "roleId": {
                    "type": "integer"
                },
                "sex": {
                    "type": "string"
                },
                "status": {
                    "type": "string",
                    "default": "1"
                },
                "updateBy": {
                    "type": "integer"
                },
                "userId": {
                    "description": "用户ID",
                    "type": "integer"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "dto.SysUserUpdateReq": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "createBy": {
                    "type": "integer"
                },
                "deptId": {
                    "type": "integer"
                },
                "email": {
                    "type": "string"
                },
                "nickName": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "postId": {
                    "type": "integer"
                },
                "remark": {
                    "type": "string"
                },
                "roleId": {
                    "type": "integer"
                },
                "sex": {
                    "type": "string"
                },
                "status": {
                    "type": "string",
                    "default": "1"
                },
                "updateBy": {
                    "type": "integer"
                },
                "userId": {
                    "description": "用户ID",
                    "type": "integer"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "dto.UpdateStatusReq": {
            "type": "object",
            "properties": {
                "createBy": {
                    "type": "integer"
                },
                "roleId": {
                    "description": "角色编码",
                    "type": "integer"
                },
                "status": {
                    "description": "状态",
                    "type": "string"
                },
                "updateBy": {
                    "type": "integer"
                }
            }
        },
        "dto.UpdateSysUserStatusReq": {
            "type": "object",
            "properties": {
                "createBy": {
                    "type": "integer"
                },
                "status": {
                    "type": "string"
                },
                "updateBy": {
                    "type": "integer"
                },
                "userId": {
                    "description": "用户ID",
                    "type": "integer"
                }
            }
        },
        "handler.Login": {
            "type": "object",
            "required": [
                "code",
                "password",
                "username",
                "uuid"
            ],
            "properties": {
                "code": {
                    "type": "string"
                },
                "password": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                }
            }
        },
        "models.SysApi": {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string"
                },
                "createBy": {
                    "type": "integer"
                },
                "createdAt": {
                    "type": "string"
                },
                "handle": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "path": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "updateBy": {
                    "type": "integer"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.SysConfig": {
            "type": "object",
            "properties": {
                "configKey": {
                    "type": "string"
                },
                "configName": {
                    "type": "string"
                },
                "configType": {
                    "type": "string"
                },
                "configValue": {
                    "type": "string"
                },
                "createBy": {
                    "type": "integer"
                },
                "createdAt": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "isFrontend": {
                    "type": "string"
                },
                "remark": {
                    "type": "string"
                },
                "updateBy": {
                    "type": "integer"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.SysDept": {
            "type": "object",
            "properties": {
                "children": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.SysDept"
                    }
                },
                "createBy": {
                    "type": "integer"
                },
                "createdAt": {
                    "type": "string"
                },
                "dataScope": {
                    "type": "string"
                },
                "deptId": {
                    "description": "部门编码",
                    "type": "integer"
                },
                "deptName": {
                    "description": "部门名称",
                    "type": "string"
                },
                "deptPath": {
                    "type": "string"
                },
                "email": {
                    "description": "邮箱",
                    "type": "string"
                },
                "leader": {
                    "description": "负责人",
                    "type": "string"
                },
                "params": {
                    "type": "string"
                },
                "parentId": {
                    "description": "上级部门",
                    "type": "integer"
                },
                "phone": {
                    "description": "手机",
                    "type": "string"
                },
                "sort": {
                    "description": "排序",
                    "type": "integer"
                },
                "status": {
                    "description": "状态",
                    "type": "integer"
                },
                "updateBy": {
                    "type": "integer"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.SysMenu": {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string"
                },
                "apis": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "breadcrumb": {
                    "type": "string"
                },
                "children": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.SysMenu"
                    }
                },
                "component": {
                    "type": "string"
                },
                "createBy": {
                    "type": "integer"
                },
                "createdAt": {
                    "type": "string"
                },
                "dataScope": {
                    "type": "string"
                },
                "icon": {
                    "type": "string"
                },
                "isFrame": {
                    "type": "string"
                },
                "is_select": {
                    "type": "boolean"
                },
                "menuId": {
                    "type": "integer"
                },
                "menuName": {
                    "type": "string"
                },
                "menuType": {
                    "type": "string"
                },
                "noCache": {
                    "type": "boolean"
                },
                "params": {
                    "type": "string"
                },
                "parentId": {
                    "type": "integer"
                },
                "path": {
                    "type": "string"
                },
                "paths": {
                    "type": "string"
                },
                "permission": {
                    "type": "string"
                },
                "roleId": {
                    "type": "integer"
                },
                "sort": {
                    "type": "integer"
                },
                "sysApi": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.SysApi"
                    }
                },
                "title": {
                    "type": "string"
                },
                "updateBy": {
                    "type": "integer"
                },
                "updatedAt": {
                    "type": "string"
                },
                "visible": {
                    "type": "string"
                }
            }
        },
        "response.Page": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "pageIndex": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                }
            }
        },
        "response.Response": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "msg": {
                    "type": "string"
                },
                "requestId": {
                    "description": "数据集",
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "tools.Params": {
            "type": "object",
            "properties": {
                "treeCode": {
                    "type": "string"
                },
                "treeName": {
                    "type": "string"
                },
                "treeParentCode": {
                    "type": "string"
                }
            }
        },
        "tools.SysColumns": {
            "type": "object",
            "properties": {
                "columnComment": {
                    "type": "string"
                },
                "columnId": {
                    "type": "integer"
                },
                "columnName": {
                    "type": "string"
                },
                "columnType": {
                    "type": "string"
                },
                "createBy": {
                    "type": "integer"
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "type": "string"
                },
                "dictType": {
                    "type": "string"
                },
                "edit": {
                    "type": "boolean"
                },
                "fkCol": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/tools.SysColumns"
                    }
                },
                "fkLabelId": {
                    "type": "string"
                },
                "fkLabelName": {
                    "type": "string"
                },
                "fkTableName": {
                    "type": "string"
                },
                "fkTableNameClass": {
                    "type": "string"
                },
                "fkTableNamePackage": {
                    "type": "string"
                },
                "goField": {
                    "type": "string"
                },
                "goType": {
                    "type": "string"
                },
                "htmlType": {
                    "type": "string"
                },
                "increment": {
                    "type": "boolean"
                },
                "insert": {
                    "type": "boolean"
                },
                "isEdit": {
                    "type": "string"
                },
                "isIncrement": {
                    "type": "string"
                },
                "isInsert": {
                    "type": "string"
                },
                "isList": {
                    "type": "string"
                },
                "isPk": {
                    "type": "string"
                },
                "isQuery": {
                    "type": "string"
                },
                "isRequired": {
                    "type": "string"
                },
                "jsonField": {
                    "type": "string"
                },
                "list": {
                    "type": "string"
                },
                "pk": {
                    "type": "boolean"
                },
                "query": {
                    "type": "boolean"
                },
                "queryType": {
                    "type": "string"
                },
                "remark": {
                    "type": "string"
                },
                "required": {
                    "type": "boolean"
                },
                "sort": {
                    "type": "integer"
                },
                "superColumn": {
                    "type": "boolean"
                },
                "tableId": {
                    "type": "integer"
                },
                "updateBy": {
                    "type": "integer"
                },
                "updatedAt": {
                    "type": "string"
                },
                "usableColumn": {
                    "type": "boolean"
                }
            }
        },
        "tools.SysTables": {
            "type": "object",
            "properties": {
                "businessName": {
                    "type": "string"
                },
                "className": {
                    "description": "类名",
                    "type": "string"
                },
                "columns": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/tools.SysColumns"
                    }
                },
                "createBy": {
                    "type": "integer"
                },
                "createdAt": {
                    "type": "string"
                },
                "crud": {
                    "type": "boolean"
                },
                "dataScope": {
                    "type": "string"
                },
                "deletedAt": {
                    "type": "string"
                },
                "functionAuthor": {
                    "description": "功能作者",
                    "type": "string"
                },
                "functionName": {
                    "description": "功能名称",
                    "type": "string"
                },
                "isActions": {
                    "type": "integer"
                },
                "isAuth": {
                    "type": "integer"
                },
                "isDataScope": {
                    "type": "integer"
                },
                "isLogicalDelete": {
                    "type": "string"
                },
                "logicalDelete": {
                    "type": "boolean"
                },
                "logicalDeleteColumn": {
                    "type": "string"
                },
                "moduleFrontName": {
                    "description": "前端文件名",
                    "type": "string"
                },
                "moduleName": {
                    "description": "go文件名",
                    "type": "string"
                },
                "options": {
                    "type": "string"
                },
                "packageName": {
                    "description": "包名",
                    "type": "string"
                },
                "params": {
                    "$ref": "#/definitions/tools.Params"
                },
                "pkColumn": {
                    "type": "string"
                },
                "pkGoField": {
                    "type": "string"
                },
                "pkJsonField": {
                    "type": "string"
                },
                "remark": {
                    "type": "string"
                },
                "tableComment": {
                    "description": "表备注",
                    "type": "string"
                },
                "tableId": {
                    "description": "表编码",
                    "type": "integer"
                },
                "tableName": {
                    "description": "表名称",
                    "type": "string"
                },
                "tplCategory": {
                    "type": "string"
                },
                "tree": {
                    "type": "boolean"
                },
                "treeCode": {
                    "type": "string"
                },
                "treeName": {
                    "type": "string"
                },
                "treeParentCode": {
                    "type": "string"
                },
                "updateBy": {
                    "type": "integer"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "Bearer": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfoadmin holds exported Swagger Info so clients can modify it
var SwaggerInfoadmin = &swag.Spec{
	Version:          "2.0.0",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "go-admin API",
	Description:      "基于Gin + Vue + Element UI的前后端分离权限管理系统的接口文档\n添加qq群: 521386980 进入技术交流群 请先star，谢谢！",
	InfoInstanceName: "admin",
	SwaggerTemplate:  docTemplateadmin,
}

func init() {
	swag.Register(SwaggerInfoadmin.InstanceName(), SwaggerInfoadmin)
}
