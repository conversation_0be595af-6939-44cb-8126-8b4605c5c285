<template>
  <div class="dashboard-container">
    <div class="dashboard-text">
      <h1>欢迎使用 Go Admin</h1>
      <p>这是一个基于 Vue 3 + Vite + TypeScript + Element Plus 构建的现代化管理后台系统</p>
    </div>

    <el-row :gutter="20" class="panel-group">
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-people">
            <SvgIcon name="peoples" class="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">用户数</div>
            <CountTo :start-val="0" :end-val="102400" class="card-panel-num" />
          </div>
        </div>
      </el-col>

      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-message">
            <SvgIcon name="message" class="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">消息数</div>
            <CountTo :start-val="0" :end-val="81212" class="card-panel-num" />
          </div>
        </div>
      </el-col>

      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-money">
            <SvgIcon name="money" class="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">收入</div>
            <CountTo :start-val="0" :end-val="9280" class="card-panel-num" />
          </div>
        </div>
      </el-col>

      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-shopping">
            <SvgIcon name="shopping" class="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">订单数</div>
            <CountTo :start-val="0" :end-val="13600" class="card-panel-num" />
          </div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :xs="24" :sm="24" :lg="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>访问量统计</span>
            </div>
          </template>
          <div class="chart-container">
            <LineChart />
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :lg="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>销售额统计</span>
            </div>
          </template>
          <div class="chart-container">
            <BarChart />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from '@/components/SvgIcon/index.vue'
import CountTo from '@/components/CountTo/index.vue'
import LineChart from './components/LineChart.vue'
import BarChart from './components/BarChart.vue'
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;

  .dashboard-text {
    text-align: center;
    margin-bottom: 40px;

    h1 {
      font-size: 32px;
      color: #303133;
      margin-bottom: 16px;
    }

    p {
      font-size: 16px;
      color: #606266;
    }
  }

  .panel-group {
    margin-bottom: 20px;

    .card-panel-col {
      margin-bottom: 32px;
    }

    .card-panel {
      height: 108px;
      cursor: pointer;
      font-size: 12px;
      position: relative;
      overflow: hidden;
      color: #666;
      background: #fff;
      box-shadow: 4px 4px 40px rgba(0, 0, 0, 0.05);
      border-color: rgba(0, 0, 0, 0.05);
      border-radius: 8px;
      padding: 20px;
      display: flex;
      align-items: center;

      &:hover {
        .card-panel-icon-wrapper {
          color: #fff;
        }

        .icon-people {
          background: #40c9c6;
        }

        .icon-message {
          background: #36a3f7;
        }

        .icon-money {
          background: #f4516c;
        }

        .icon-shopping {
          background: #34bfa3;
        }
      }

      .card-panel-icon-wrapper {
        float: left;
        overflow: hidden;
        padding: 16px;
        transition: all 0.38s ease-out;
        border-radius: 6px;
        margin-right: 20px;
      }

      .card-panel-icon {
        float: left;
        font-size: 48px;
      }

      .card-panel-description {
        float: right;
        font-weight: bold;
        margin: 26px 0;

        .card-panel-text {
          line-height: 18px;
          color: rgba(0, 0, 0, 0.45);
          font-size: 16px;
          margin-bottom: 12px;
        }

        .card-panel-num {
          font-size: 20px;
          color: rgba(0, 0, 0, 0.85);
        }
      }
    }
  }

  .chart-container {
    height: 300px;
  }
}
</style>
