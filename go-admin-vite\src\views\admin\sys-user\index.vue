<template>
  <div class="app-container">
    <el-card class="box-card">
      <el-row :gutter="20">
        <!--部门数据-->
        <el-col :span="4" :xs="24">
          <div class="head-container">
            <el-input
              v-model="deptName"
              placeholder="请输入部门名称"
              clearable
              prefix-icon="Search"
              style="margin-bottom: 20px"
            />
          </div>
          <div class="head-container">
            <el-tree
              ref="deptTreeRef"
              :data="deptOptions"
              :props="defaultProps"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              default-expand-all
              @node-click="handleNodeClick"
            />
          </div>
        </el-col>
        <!--用户数据-->
        <el-col :span="20" :xs="24">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
            <el-form-item label="用户名称" prop="username">
              <el-input
                v-model="queryParams.username"
                placeholder="请输入用户名称"
                clearable
                style="width: 160px"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="手机号码" prop="phone">
              <el-input
                v-model="queryParams.phone"
                placeholder="请输入手机号码"
                clearable
                style="width: 160px"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="用户状态"
                clearable
                style="width: 160px"
              >
                <el-option
                  v-for="dict in statusOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="Plus"
                @click="handleAdd"
              >新增</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="success"
                plain
                icon="Edit"
                :disabled="single"
                @click="handleUpdate"
              >修改</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                plain
                icon="Delete"
                :disabled="multiple"
                @click="handleDelete"
              >删除</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="info"
                plain
                icon="Upload"
                @click="handleImport"
              >导入</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="warning"
                plain
                icon="Download"
                @click="handleExport"
              >导出</el-button>
            </el-col>
          </el-row>

          <el-table
            v-loading="loading"
            :data="userList"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column label="用户编号" align="center" key="userId" prop="userId" v-if="columns[0].visible" />
            <el-table-column label="用户名称" align="center" key="username" prop="username" v-if="columns[1].visible" :show-overflow-tooltip="true" />
            <el-table-column label="用户昵称" align="center" key="nickName" prop="nickName" v-if="columns[2].visible" :show-overflow-tooltip="true" />
            <el-table-column label="部门" align="center" key="deptName" prop="dept.deptName" v-if="columns[3].visible" :show-overflow-tooltip="true" />
            <el-table-column label="手机号码" align="center" key="phone" prop="phone" v-if="columns[4].visible" width="120" />
            <el-table-column label="状态" align="center" key="status" v-if="columns[5].visible">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.status"
                  active-value="2"
                  inactive-value="1"
                  @change="handleStatusChange(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" prop="createTime" v-if="columns[6].visible" width="160">
              <template #default="scope">
                <span>{{ parseTime(scope.row.createTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              width="160"
              class-name="small-padding fixed-width"
            >
              <template #default="scope">
                <el-button
                  type="primary"
                  icon="Edit"
                  @click="handleUpdate(scope.row)"
                  link
                >修改</el-button>
                <el-button
                  type="primary"
                  icon="Delete"
                  @click="handleDelete(scope.row)"
                  link
                >删除</el-button>
                <el-dropdown size="small" @command="(command) => handleCommand(command, scope.row)" v-if="scope.row.userId !== 1">
                  <el-button type="primary" link>
                    <el-icon><DArrowRight /></el-icon>更多
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="handleResetPwd" icon="Key">重置密码</el-dropdown-item>
                      <el-dropdown-item command="handleAuthRole" icon="CircleCheck">分配角色</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type ElTree } from 'element-plus'
import { DArrowRight } from '@element-plus/icons-vue'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination/index.vue'
import { listUser, changeUserStatus, delUser, resetUserPwd, type SysUser, type UserQuery } from '@/api/admin/sys-user'
import { treeselect } from '@/api/admin/sys-dept'

// 响应式数据
const loading = ref(false)
const userList = ref<SysUser[]>([])
const deptOptions = ref<any[]>([])

const statusOptions = ref([
  { value: '1', label: '停用' },
  { value: '2', label: '正常' },
])

const columns = ref([
  { key: 0, label: '用户编号', visible: true },
  { key: 1, label: '用户名称', visible: true },
  { key: 2, label: '用户昵称', visible: true },
  { key: 3, label: '部门', visible: true },
  { key: 4, label: '手机号码', visible: true },
  { key: 5, label: '状态', visible: true },
  { key: 6, label: '创建时间', visible: true },
])

const total = ref(0)
const single = ref(true)
const multiple = ref(true)
const deptName = ref('')

const queryParams = reactive<UserQuery>({
  pageNum: 1,
  pageSize: 10,
  username: '',
  phone: '',
  status: '',
  deptId: undefined,
})

const defaultProps = {
  children: 'children',
  label: 'label',
}

const queryFormRef = ref<FormInstance>()
const deptTreeRef = ref<InstanceType<typeof ElTree>>()

// 监听部门名称变化，过滤部门树
watch(deptName, (val) => {
  deptTreeRef.value?.filter(val)
})

async function getList() {
  loading.value = true
  try {
    const response = await listUser(queryParams)
    userList.value = response.list || []
    total.value = response.count || 0
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

function resetQuery() {
  queryFormRef.value?.resetFields()
  handleQuery()
}

function handleSelectionChange(selection: any[]) {
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

function handleNodeClick(data: any) {
  queryParams.deptId = data.id
  handleQuery()
}

function filterNode(value: string, data: any) {
  if (!value) return true
  return data.label.includes(value)
}

async function handleStatusChange(row: SysUser) {
  const text = row.status === '2' ? '启用' : '停用'
  try {
    await changeUserStatus(row.userId, row.status)
    ElMessage.success(`${text}成功`)
  } catch (error) {
    console.error('修改用户状态失败:', error)
    ElMessage.error(`${text}失败`)
    // 恢复原状态
    row.status = row.status === '2' ? '1' : '2'
  }
}

function handleAdd() {
  ElMessage.success('打开新增用户对话框')
  // 这里可以打开新增用户的对话框
  console.log('新增用户')
}

function handleUpdate(row?: any) {
  if (row) {
    ElMessage.success(`编辑用户: ${row.userName}`)
    console.log('编辑用户:', row)
  } else {
    ElMessage.warning('请选择要编辑的用户')
  }
}

async function handleDelete(row?: SysUser) {
  const userIds = row ? [row.userId] : userList.value.filter((_, index) =>
    (document.querySelectorAll('.el-table__body .el-checkbox__input')[index] as HTMLInputElement)?.checked
  ).map(user => user.userId)

  if (userIds.length === 0) {
    ElMessage.warning('请选择要删除的用户')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定删除选中的${userIds.length}个用户吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await delUser(userIds)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

function handleImport() {
  ElMessage.info('导入功能待开发')
}

function handleExport() {
  ElMessage.info('导出功能待开发')
}

async function handleCommand(command: string, row: SysUser) {
  switch (command) {
    case 'handleResetPwd':
      try {
        await ElMessageBox.confirm(
          `确定重置用户"${row.username}"的密码吗？`,
          '警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        await resetUserPwd(row.userId, '123456')
        ElMessage.success('重置密码成功，新密码为：123456')
      } catch (error) {
        if (error !== 'cancel') {
          console.error('重置密码失败:', error)
          ElMessage.error('重置密码失败')
        }
      }
      break
    case 'handleAuthRole':
      ElMessage.info('分配角色功能待开发')
      break
  }
}

// 获取部门树数据
async function getDeptTree() {
  try {
    const data = await treeselect()
    deptOptions.value = data.map(dept => ({
      id: dept.deptId,
      label: dept.deptName,
      children: dept.children?.map(child => ({
        id: child.deptId,
        label: child.deptName,
        children: child.children?.map(grandChild => ({
          id: grandChild.deptId,
          label: grandChild.deptName,
        }))
      }))
    }))
  } catch (error) {
    console.error('获取部门树失败:', error)
  }
}

onMounted(() => {
  getList()
  getDeptTree()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
</style>
